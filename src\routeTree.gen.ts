/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { createFileRoute } from '@tanstack/react-router'

import { Route as rootRouteImport } from './routes/__root'
import { Route as IndexRouteImport } from './routes/index'
import { Route as AccountLayoutRouteImport } from './routes/account/_layout'
import { Route as appMainlayoutRouteImport } from './routes/(app)/_mainlayout'
import { Route as appPricingSelectplanRouteImport } from './routes/(app)/_pricing/selectplan'
import { Route as appPricingPaymentSuccessRouteImport } from './routes/(app)/_pricing/payment-success'
import { Route as appMainlayoutSavedRouteImport } from './routes/(app)/_mainlayout/saved'
import { Route as appMainlayoutMcqsRouteImport } from './routes/(app)/_mainlayout/mcqs'
import { Route as appMainlayoutLearnRouteImport } from './routes/(app)/_mainlayout/learn'
import { Route as appMainlayoutEditAccountRouteImport } from './routes/(app)/_mainlayout/edit-account'
import { Route as appMainlayoutDashboardRouteImport } from './routes/(app)/_mainlayout/dashboard'
import { Route as appMainlayoutAttemptRouteImport } from './routes/(app)/_mainlayout/attempt'
import { Route as appMainlayoutAnalyticsRouteImport } from './routes/(app)/_mainlayout/analytics'
import { Route as appMainlayoutAccountRouteImport } from './routes/(app)/_mainlayout/account'
import { Route as appChatbotAddKeyRouteImport } from './routes/(app)/_chatbot/add-key'
import { Route as appMainlayoutTSubjectRouteImport } from './routes/(app)/_mainlayout/t/subject'
import { Route as appMainlayoutTMockRouteImport } from './routes/(app)/_mainlayout/t/mock'
import { Route as appMainlayoutTCustomRouteImport } from './routes/(app)/_mainlayout/t/custom'
import { Route as appMainlayoutTAiNewRouteImport } from './routes/(app)/_mainlayout/t/ai-new'
import { Route as appMainlayoutTAiRouteImport } from './routes/(app)/_mainlayout/t/ai'
import { Route as appMainlayoutResourceResourceIdRouteImport } from './routes/(app)/_mainlayout/resource/$resourceId'
import { Route as appMainlayoutQuizQuizIdRouteImport } from './routes/(app)/_mainlayout/quiz/$quizId'

const AccountRouteImport = createFileRoute('/account')()
const appRouteImport = createFileRoute('/(app)')()
const AccountLayoutVerifyemailLazyRouteImport = createFileRoute(
  '/account/_layout/verifyemail',
)()
const AccountLayoutResetpasswordLazyRouteImport = createFileRoute(
  '/account/_layout/resetpassword',
)()
const AccountLayoutRegisterLazyRouteImport = createFileRoute(
  '/account/_layout/register',
)()
const AccountLayoutPersonalinfoLazyRouteImport = createFileRoute(
  '/account/_layout/personalinfo',
)()
const AccountLayoutLoginLazyRouteImport = createFileRoute(
  '/account/_layout/login',
)()

const AccountRoute = AccountRouteImport.update({
  id: '/account',
  path: '/account',
  getParentRoute: () => rootRouteImport,
} as any)
const appRoute = appRouteImport.update({
  id: '/(app)',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const AccountLayoutRoute = AccountLayoutRouteImport.update({
  id: '/_layout',
  getParentRoute: () => AccountRoute,
} as any)
const appMainlayoutRoute = appMainlayoutRouteImport.update({
  id: '/_mainlayout',
  getParentRoute: () => appRoute,
} as any)
const AccountLayoutVerifyemailLazyRoute =
  AccountLayoutVerifyemailLazyRouteImport.update({
    id: '/verifyemail',
    path: '/verifyemail',
    getParentRoute: () => AccountLayoutRoute,
  } as any).lazy(() =>
    import('./routes/account/_layout.verifyemail.lazy').then((d) => d.Route),
  )
const AccountLayoutResetpasswordLazyRoute =
  AccountLayoutResetpasswordLazyRouteImport.update({
    id: '/resetpassword',
    path: '/resetpassword',
    getParentRoute: () => AccountLayoutRoute,
  } as any).lazy(() =>
    import('./routes/account/_layout.resetpassword.lazy').then((d) => d.Route),
  )
const AccountLayoutRegisterLazyRoute =
  AccountLayoutRegisterLazyRouteImport.update({
    id: '/register',
    path: '/register',
    getParentRoute: () => AccountLayoutRoute,
  } as any).lazy(() =>
    import('./routes/account/_layout.register.lazy').then((d) => d.Route),
  )
const AccountLayoutPersonalinfoLazyRoute =
  AccountLayoutPersonalinfoLazyRouteImport.update({
    id: '/personalinfo',
    path: '/personalinfo',
    getParentRoute: () => AccountLayoutRoute,
  } as any).lazy(() =>
    import('./routes/account/_layout.personalinfo.lazy').then((d) => d.Route),
  )
const AccountLayoutLoginLazyRoute = AccountLayoutLoginLazyRouteImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => AccountLayoutRoute,
} as any).lazy(() =>
  import('./routes/account/_layout.login.lazy').then((d) => d.Route),
)
const appPricingSelectplanRoute = appPricingSelectplanRouteImport.update({
  id: '/_pricing/selectplan',
  path: '/selectplan',
  getParentRoute: () => appRoute,
} as any)
const appPricingPaymentSuccessRoute =
  appPricingPaymentSuccessRouteImport.update({
    id: '/_pricing/payment-success',
    path: '/payment-success',
    getParentRoute: () => appRoute,
  } as any)
const appMainlayoutSavedRoute = appMainlayoutSavedRouteImport.update({
  id: '/saved',
  path: '/saved',
  getParentRoute: () => appMainlayoutRoute,
} as any)
const appMainlayoutMcqsRoute = appMainlayoutMcqsRouteImport.update({
  id: '/mcqs',
  path: '/mcqs',
  getParentRoute: () => appMainlayoutRoute,
} as any)
const appMainlayoutLearnRoute = appMainlayoutLearnRouteImport.update({
  id: '/learn',
  path: '/learn',
  getParentRoute: () => appMainlayoutRoute,
} as any)
const appMainlayoutEditAccountRoute =
  appMainlayoutEditAccountRouteImport.update({
    id: '/edit-account',
    path: '/edit-account',
    getParentRoute: () => appMainlayoutRoute,
  } as any)
const appMainlayoutDashboardRoute = appMainlayoutDashboardRouteImport.update({
  id: '/dashboard',
  path: '/dashboard',
  getParentRoute: () => appMainlayoutRoute,
} as any)
const appMainlayoutAttemptRoute = appMainlayoutAttemptRouteImport.update({
  id: '/attempt',
  path: '/attempt',
  getParentRoute: () => appMainlayoutRoute,
} as any)
const appMainlayoutAnalyticsRoute = appMainlayoutAnalyticsRouteImport.update({
  id: '/analytics',
  path: '/analytics',
  getParentRoute: () => appMainlayoutRoute,
} as any)
const appMainlayoutAccountRoute = appMainlayoutAccountRouteImport.update({
  id: '/account',
  path: '/account',
  getParentRoute: () => appMainlayoutRoute,
} as any)
const appChatbotAddKeyRoute = appChatbotAddKeyRouteImport.update({
  id: '/_chatbot/add-key',
  path: '/add-key',
  getParentRoute: () => appRoute,
} as any)
const appMainlayoutTSubjectRoute = appMainlayoutTSubjectRouteImport.update({
  id: '/t/subject',
  path: '/t/subject',
  getParentRoute: () => appMainlayoutRoute,
} as any)
const appMainlayoutTMockRoute = appMainlayoutTMockRouteImport.update({
  id: '/t/mock',
  path: '/t/mock',
  getParentRoute: () => appMainlayoutRoute,
} as any)
const appMainlayoutTCustomRoute = appMainlayoutTCustomRouteImport.update({
  id: '/t/custom',
  path: '/t/custom',
  getParentRoute: () => appMainlayoutRoute,
} as any)
const appMainlayoutTAiNewRoute = appMainlayoutTAiNewRouteImport.update({
  id: '/t/ai-new',
  path: '/t/ai-new',
  getParentRoute: () => appMainlayoutRoute,
} as any)
const appMainlayoutTAiRoute = appMainlayoutTAiRouteImport.update({
  id: '/t/ai',
  path: '/t/ai',
  getParentRoute: () => appMainlayoutRoute,
} as any)
const appMainlayoutResourceResourceIdRoute =
  appMainlayoutResourceResourceIdRouteImport.update({
    id: '/resource/$resourceId',
    path: '/resource/$resourceId',
    getParentRoute: () => appMainlayoutRoute,
  } as any)
const appMainlayoutQuizQuizIdRoute = appMainlayoutQuizQuizIdRouteImport.update({
  id: '/quiz/$quizId',
  path: '/quiz/$quizId',
  getParentRoute: () => appMainlayoutRoute,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof appMainlayoutRouteWithChildren
  '/account': typeof appMainlayoutAccountRoute
  '/add-key': typeof appChatbotAddKeyRoute
  '/analytics': typeof appMainlayoutAnalyticsRoute
  '/attempt': typeof appMainlayoutAttemptRoute
  '/dashboard': typeof appMainlayoutDashboardRoute
  '/edit-account': typeof appMainlayoutEditAccountRoute
  '/learn': typeof appMainlayoutLearnRoute
  '/mcqs': typeof appMainlayoutMcqsRoute
  '/saved': typeof appMainlayoutSavedRoute
  '/payment-success': typeof appPricingPaymentSuccessRoute
  '/selectplan': typeof appPricingSelectplanRoute
  '/account/login': typeof AccountLayoutLoginLazyRoute
  '/account/personalinfo': typeof AccountLayoutPersonalinfoLazyRoute
  '/account/register': typeof AccountLayoutRegisterLazyRoute
  '/account/resetpassword': typeof AccountLayoutResetpasswordLazyRoute
  '/account/verifyemail': typeof AccountLayoutVerifyemailLazyRoute
  '/quiz/$quizId': typeof appMainlayoutQuizQuizIdRoute
  '/resource/$resourceId': typeof appMainlayoutResourceResourceIdRoute
  '/t/ai': typeof appMainlayoutTAiRoute
  '/t/ai-new': typeof appMainlayoutTAiNewRoute
  '/t/custom': typeof appMainlayoutTCustomRoute
  '/t/mock': typeof appMainlayoutTMockRoute
  '/t/subject': typeof appMainlayoutTSubjectRoute
}
export interface FileRoutesByTo {
  '/': typeof appMainlayoutRouteWithChildren
  '/account': typeof appMainlayoutAccountRoute
  '/add-key': typeof appChatbotAddKeyRoute
  '/analytics': typeof appMainlayoutAnalyticsRoute
  '/attempt': typeof appMainlayoutAttemptRoute
  '/dashboard': typeof appMainlayoutDashboardRoute
  '/edit-account': typeof appMainlayoutEditAccountRoute
  '/learn': typeof appMainlayoutLearnRoute
  '/mcqs': typeof appMainlayoutMcqsRoute
  '/saved': typeof appMainlayoutSavedRoute
  '/payment-success': typeof appPricingPaymentSuccessRoute
  '/selectplan': typeof appPricingSelectplanRoute
  '/account/login': typeof AccountLayoutLoginLazyRoute
  '/account/personalinfo': typeof AccountLayoutPersonalinfoLazyRoute
  '/account/register': typeof AccountLayoutRegisterLazyRoute
  '/account/resetpassword': typeof AccountLayoutResetpasswordLazyRoute
  '/account/verifyemail': typeof AccountLayoutVerifyemailLazyRoute
  '/quiz/$quizId': typeof appMainlayoutQuizQuizIdRoute
  '/resource/$resourceId': typeof appMainlayoutResourceResourceIdRoute
  '/t/ai': typeof appMainlayoutTAiRoute
  '/t/ai-new': typeof appMainlayoutTAiNewRoute
  '/t/custom': typeof appMainlayoutTCustomRoute
  '/t/mock': typeof appMainlayoutTMockRoute
  '/t/subject': typeof appMainlayoutTSubjectRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/(app)': typeof appRouteWithChildren
  '/(app)/_mainlayout': typeof appMainlayoutRouteWithChildren
  '/account': typeof AccountRouteWithChildren
  '/account/_layout': typeof AccountLayoutRouteWithChildren
  '/(app)/_chatbot/add-key': typeof appChatbotAddKeyRoute
  '/(app)/_mainlayout/account': typeof appMainlayoutAccountRoute
  '/(app)/_mainlayout/analytics': typeof appMainlayoutAnalyticsRoute
  '/(app)/_mainlayout/attempt': typeof appMainlayoutAttemptRoute
  '/(app)/_mainlayout/dashboard': typeof appMainlayoutDashboardRoute
  '/(app)/_mainlayout/edit-account': typeof appMainlayoutEditAccountRoute
  '/(app)/_mainlayout/learn': typeof appMainlayoutLearnRoute
  '/(app)/_mainlayout/mcqs': typeof appMainlayoutMcqsRoute
  '/(app)/_mainlayout/saved': typeof appMainlayoutSavedRoute
  '/(app)/_pricing/payment-success': typeof appPricingPaymentSuccessRoute
  '/(app)/_pricing/selectplan': typeof appPricingSelectplanRoute
  '/account/_layout/login': typeof AccountLayoutLoginLazyRoute
  '/account/_layout/personalinfo': typeof AccountLayoutPersonalinfoLazyRoute
  '/account/_layout/register': typeof AccountLayoutRegisterLazyRoute
  '/account/_layout/resetpassword': typeof AccountLayoutResetpasswordLazyRoute
  '/account/_layout/verifyemail': typeof AccountLayoutVerifyemailLazyRoute
  '/(app)/_mainlayout/quiz/$quizId': typeof appMainlayoutQuizQuizIdRoute
  '/(app)/_mainlayout/resource/$resourceId': typeof appMainlayoutResourceResourceIdRoute
  '/(app)/_mainlayout/t/ai': typeof appMainlayoutTAiRoute
  '/(app)/_mainlayout/t/ai-new': typeof appMainlayoutTAiNewRoute
  '/(app)/_mainlayout/t/custom': typeof appMainlayoutTCustomRoute
  '/(app)/_mainlayout/t/mock': typeof appMainlayoutTMockRoute
  '/(app)/_mainlayout/t/subject': typeof appMainlayoutTSubjectRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/account'
    | '/add-key'
    | '/analytics'
    | '/attempt'
    | '/dashboard'
    | '/edit-account'
    | '/learn'
    | '/mcqs'
    | '/saved'
    | '/payment-success'
    | '/selectplan'
    | '/account/login'
    | '/account/personalinfo'
    | '/account/register'
    | '/account/resetpassword'
    | '/account/verifyemail'
    | '/quiz/$quizId'
    | '/resource/$resourceId'
    | '/t/ai'
    | '/t/ai-new'
    | '/t/custom'
    | '/t/mock'
    | '/t/subject'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/account'
    | '/add-key'
    | '/analytics'
    | '/attempt'
    | '/dashboard'
    | '/edit-account'
    | '/learn'
    | '/mcqs'
    | '/saved'
    | '/payment-success'
    | '/selectplan'
    | '/account/login'
    | '/account/personalinfo'
    | '/account/register'
    | '/account/resetpassword'
    | '/account/verifyemail'
    | '/quiz/$quizId'
    | '/resource/$resourceId'
    | '/t/ai'
    | '/t/ai-new'
    | '/t/custom'
    | '/t/mock'
    | '/t/subject'
  id:
    | '__root__'
    | '/'
    | '/(app)'
    | '/(app)/_mainlayout'
    | '/account'
    | '/account/_layout'
    | '/(app)/_chatbot/add-key'
    | '/(app)/_mainlayout/account'
    | '/(app)/_mainlayout/analytics'
    | '/(app)/_mainlayout/attempt'
    | '/(app)/_mainlayout/dashboard'
    | '/(app)/_mainlayout/edit-account'
    | '/(app)/_mainlayout/learn'
    | '/(app)/_mainlayout/mcqs'
    | '/(app)/_mainlayout/saved'
    | '/(app)/_pricing/payment-success'
    | '/(app)/_pricing/selectplan'
    | '/account/_layout/login'
    | '/account/_layout/personalinfo'
    | '/account/_layout/register'
    | '/account/_layout/resetpassword'
    | '/account/_layout/verifyemail'
    | '/(app)/_mainlayout/quiz/$quizId'
    | '/(app)/_mainlayout/resource/$resourceId'
    | '/(app)/_mainlayout/t/ai'
    | '/(app)/_mainlayout/t/ai-new'
    | '/(app)/_mainlayout/t/custom'
    | '/(app)/_mainlayout/t/mock'
    | '/(app)/_mainlayout/t/subject'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  appRoute: typeof appRouteWithChildren
  AccountRoute: typeof AccountRouteWithChildren
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/account': {
      id: '/account'
      path: '/account'
      fullPath: '/account'
      preLoaderRoute: typeof AccountRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/(app)': {
      id: '/(app)'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof appRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/account/_layout': {
      id: '/account/_layout'
      path: '/account'
      fullPath: '/account'
      preLoaderRoute: typeof AccountLayoutRouteImport
      parentRoute: typeof AccountRoute
    }
    '/(app)/_mainlayout': {
      id: '/(app)/_mainlayout'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof appMainlayoutRouteImport
      parentRoute: typeof appRoute
    }
    '/account/_layout/verifyemail': {
      id: '/account/_layout/verifyemail'
      path: '/verifyemail'
      fullPath: '/account/verifyemail'
      preLoaderRoute: typeof AccountLayoutVerifyemailLazyRouteImport
      parentRoute: typeof AccountLayoutRoute
    }
    '/account/_layout/resetpassword': {
      id: '/account/_layout/resetpassword'
      path: '/resetpassword'
      fullPath: '/account/resetpassword'
      preLoaderRoute: typeof AccountLayoutResetpasswordLazyRouteImport
      parentRoute: typeof AccountLayoutRoute
    }
    '/account/_layout/register': {
      id: '/account/_layout/register'
      path: '/register'
      fullPath: '/account/register'
      preLoaderRoute: typeof AccountLayoutRegisterLazyRouteImport
      parentRoute: typeof AccountLayoutRoute
    }
    '/account/_layout/personalinfo': {
      id: '/account/_layout/personalinfo'
      path: '/personalinfo'
      fullPath: '/account/personalinfo'
      preLoaderRoute: typeof AccountLayoutPersonalinfoLazyRouteImport
      parentRoute: typeof AccountLayoutRoute
    }
    '/account/_layout/login': {
      id: '/account/_layout/login'
      path: '/login'
      fullPath: '/account/login'
      preLoaderRoute: typeof AccountLayoutLoginLazyRouteImport
      parentRoute: typeof AccountLayoutRoute
    }
    '/(app)/_pricing/selectplan': {
      id: '/(app)/_pricing/selectplan'
      path: '/selectplan'
      fullPath: '/selectplan'
      preLoaderRoute: typeof appPricingSelectplanRouteImport
      parentRoute: typeof appRoute
    }
    '/(app)/_pricing/payment-success': {
      id: '/(app)/_pricing/payment-success'
      path: '/payment-success'
      fullPath: '/payment-success'
      preLoaderRoute: typeof appPricingPaymentSuccessRouteImport
      parentRoute: typeof appRoute
    }
    '/(app)/_mainlayout/saved': {
      id: '/(app)/_mainlayout/saved'
      path: '/saved'
      fullPath: '/saved'
      preLoaderRoute: typeof appMainlayoutSavedRouteImport
      parentRoute: typeof appMainlayoutRoute
    }
    '/(app)/_mainlayout/mcqs': {
      id: '/(app)/_mainlayout/mcqs'
      path: '/mcqs'
      fullPath: '/mcqs'
      preLoaderRoute: typeof appMainlayoutMcqsRouteImport
      parentRoute: typeof appMainlayoutRoute
    }
    '/(app)/_mainlayout/learn': {
      id: '/(app)/_mainlayout/learn'
      path: '/learn'
      fullPath: '/learn'
      preLoaderRoute: typeof appMainlayoutLearnRouteImport
      parentRoute: typeof appMainlayoutRoute
    }
    '/(app)/_mainlayout/edit-account': {
      id: '/(app)/_mainlayout/edit-account'
      path: '/edit-account'
      fullPath: '/edit-account'
      preLoaderRoute: typeof appMainlayoutEditAccountRouteImport
      parentRoute: typeof appMainlayoutRoute
    }
    '/(app)/_mainlayout/dashboard': {
      id: '/(app)/_mainlayout/dashboard'
      path: '/dashboard'
      fullPath: '/dashboard'
      preLoaderRoute: typeof appMainlayoutDashboardRouteImport
      parentRoute: typeof appMainlayoutRoute
    }
    '/(app)/_mainlayout/attempt': {
      id: '/(app)/_mainlayout/attempt'
      path: '/attempt'
      fullPath: '/attempt'
      preLoaderRoute: typeof appMainlayoutAttemptRouteImport
      parentRoute: typeof appMainlayoutRoute
    }
    '/(app)/_mainlayout/analytics': {
      id: '/(app)/_mainlayout/analytics'
      path: '/analytics'
      fullPath: '/analytics'
      preLoaderRoute: typeof appMainlayoutAnalyticsRouteImport
      parentRoute: typeof appMainlayoutRoute
    }
    '/(app)/_mainlayout/account': {
      id: '/(app)/_mainlayout/account'
      path: '/account'
      fullPath: '/account'
      preLoaderRoute: typeof appMainlayoutAccountRouteImport
      parentRoute: typeof appMainlayoutRoute
    }
    '/(app)/_chatbot/add-key': {
      id: '/(app)/_chatbot/add-key'
      path: '/add-key'
      fullPath: '/add-key'
      preLoaderRoute: typeof appChatbotAddKeyRouteImport
      parentRoute: typeof appRoute
    }
    '/(app)/_mainlayout/t/subject': {
      id: '/(app)/_mainlayout/t/subject'
      path: '/t/subject'
      fullPath: '/t/subject'
      preLoaderRoute: typeof appMainlayoutTSubjectRouteImport
      parentRoute: typeof appMainlayoutRoute
    }
    '/(app)/_mainlayout/t/mock': {
      id: '/(app)/_mainlayout/t/mock'
      path: '/t/mock'
      fullPath: '/t/mock'
      preLoaderRoute: typeof appMainlayoutTMockRouteImport
      parentRoute: typeof appMainlayoutRoute
    }
    '/(app)/_mainlayout/t/custom': {
      id: '/(app)/_mainlayout/t/custom'
      path: '/t/custom'
      fullPath: '/t/custom'
      preLoaderRoute: typeof appMainlayoutTCustomRouteImport
      parentRoute: typeof appMainlayoutRoute
    }
    '/(app)/_mainlayout/t/ai-new': {
      id: '/(app)/_mainlayout/t/ai-new'
      path: '/t/ai-new'
      fullPath: '/t/ai-new'
      preLoaderRoute: typeof appMainlayoutTAiNewRouteImport
      parentRoute: typeof appMainlayoutRoute
    }
    '/(app)/_mainlayout/t/ai': {
      id: '/(app)/_mainlayout/t/ai'
      path: '/t/ai'
      fullPath: '/t/ai'
      preLoaderRoute: typeof appMainlayoutTAiRouteImport
      parentRoute: typeof appMainlayoutRoute
    }
    '/(app)/_mainlayout/resource/$resourceId': {
      id: '/(app)/_mainlayout/resource/$resourceId'
      path: '/resource/$resourceId'
      fullPath: '/resource/$resourceId'
      preLoaderRoute: typeof appMainlayoutResourceResourceIdRouteImport
      parentRoute: typeof appMainlayoutRoute
    }
    '/(app)/_mainlayout/quiz/$quizId': {
      id: '/(app)/_mainlayout/quiz/$quizId'
      path: '/quiz/$quizId'
      fullPath: '/quiz/$quizId'
      preLoaderRoute: typeof appMainlayoutQuizQuizIdRouteImport
      parentRoute: typeof appMainlayoutRoute
    }
  }
}

interface appMainlayoutRouteChildren {
  appMainlayoutAccountRoute: typeof appMainlayoutAccountRoute
  appMainlayoutAnalyticsRoute: typeof appMainlayoutAnalyticsRoute
  appMainlayoutAttemptRoute: typeof appMainlayoutAttemptRoute
  appMainlayoutDashboardRoute: typeof appMainlayoutDashboardRoute
  appMainlayoutEditAccountRoute: typeof appMainlayoutEditAccountRoute
  appMainlayoutLearnRoute: typeof appMainlayoutLearnRoute
  appMainlayoutMcqsRoute: typeof appMainlayoutMcqsRoute
  appMainlayoutSavedRoute: typeof appMainlayoutSavedRoute
  appMainlayoutQuizQuizIdRoute: typeof appMainlayoutQuizQuizIdRoute
  appMainlayoutResourceResourceIdRoute: typeof appMainlayoutResourceResourceIdRoute
  appMainlayoutTAiRoute: typeof appMainlayoutTAiRoute
  appMainlayoutTAiNewRoute: typeof appMainlayoutTAiNewRoute
  appMainlayoutTCustomRoute: typeof appMainlayoutTCustomRoute
  appMainlayoutTMockRoute: typeof appMainlayoutTMockRoute
  appMainlayoutTSubjectRoute: typeof appMainlayoutTSubjectRoute
}

const appMainlayoutRouteChildren: appMainlayoutRouteChildren = {
  appMainlayoutAccountRoute: appMainlayoutAccountRoute,
  appMainlayoutAnalyticsRoute: appMainlayoutAnalyticsRoute,
  appMainlayoutAttemptRoute: appMainlayoutAttemptRoute,
  appMainlayoutDashboardRoute: appMainlayoutDashboardRoute,
  appMainlayoutEditAccountRoute: appMainlayoutEditAccountRoute,
  appMainlayoutLearnRoute: appMainlayoutLearnRoute,
  appMainlayoutMcqsRoute: appMainlayoutMcqsRoute,
  appMainlayoutSavedRoute: appMainlayoutSavedRoute,
  appMainlayoutQuizQuizIdRoute: appMainlayoutQuizQuizIdRoute,
  appMainlayoutResourceResourceIdRoute: appMainlayoutResourceResourceIdRoute,
  appMainlayoutTAiRoute: appMainlayoutTAiRoute,
  appMainlayoutTAiNewRoute: appMainlayoutTAiNewRoute,
  appMainlayoutTCustomRoute: appMainlayoutTCustomRoute,
  appMainlayoutTMockRoute: appMainlayoutTMockRoute,
  appMainlayoutTSubjectRoute: appMainlayoutTSubjectRoute,
}

const appMainlayoutRouteWithChildren = appMainlayoutRoute._addFileChildren(
  appMainlayoutRouteChildren,
)

interface appRouteChildren {
  appMainlayoutRoute: typeof appMainlayoutRouteWithChildren
  appChatbotAddKeyRoute: typeof appChatbotAddKeyRoute
  appPricingPaymentSuccessRoute: typeof appPricingPaymentSuccessRoute
  appPricingSelectplanRoute: typeof appPricingSelectplanRoute
}

const appRouteChildren: appRouteChildren = {
  appMainlayoutRoute: appMainlayoutRouteWithChildren,
  appChatbotAddKeyRoute: appChatbotAddKeyRoute,
  appPricingPaymentSuccessRoute: appPricingPaymentSuccessRoute,
  appPricingSelectplanRoute: appPricingSelectplanRoute,
}

const appRouteWithChildren = appRoute._addFileChildren(appRouteChildren)

interface AccountLayoutRouteChildren {
  AccountLayoutLoginLazyRoute: typeof AccountLayoutLoginLazyRoute
  AccountLayoutPersonalinfoLazyRoute: typeof AccountLayoutPersonalinfoLazyRoute
  AccountLayoutRegisterLazyRoute: typeof AccountLayoutRegisterLazyRoute
  AccountLayoutResetpasswordLazyRoute: typeof AccountLayoutResetpasswordLazyRoute
  AccountLayoutVerifyemailLazyRoute: typeof AccountLayoutVerifyemailLazyRoute
}

const AccountLayoutRouteChildren: AccountLayoutRouteChildren = {
  AccountLayoutLoginLazyRoute: AccountLayoutLoginLazyRoute,
  AccountLayoutPersonalinfoLazyRoute: AccountLayoutPersonalinfoLazyRoute,
  AccountLayoutRegisterLazyRoute: AccountLayoutRegisterLazyRoute,
  AccountLayoutResetpasswordLazyRoute: AccountLayoutResetpasswordLazyRoute,
  AccountLayoutVerifyemailLazyRoute: AccountLayoutVerifyemailLazyRoute,
}

const AccountLayoutRouteWithChildren = AccountLayoutRoute._addFileChildren(
  AccountLayoutRouteChildren,
)

interface AccountRouteChildren {
  AccountLayoutRoute: typeof AccountLayoutRouteWithChildren
}

const AccountRouteChildren: AccountRouteChildren = {
  AccountLayoutRoute: AccountLayoutRouteWithChildren,
}

const AccountRouteWithChildren =
  AccountRoute._addFileChildren(AccountRouteChildren)

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  appRoute: appRouteWithChildren,
  AccountRoute: AccountRouteWithChildren,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
