export const cities = [
	{ label: "Other", value: "other" },
	{ label: "Karachi", value: "Karachi" },
	{ label: "Lahore", value: "Lahore" },
	{ label: "Faisalabad", value: "Faisalabad" },
	{ label: "Rawalpindi", value: "Rawalpindi" },
	{ label: "Multan", value: "Multan" },
	{ label: "Gujranwala", value: "Gujranwala" },
	{ label: "Hyderabad", value: "Hyderabad" },
	{ label: "Peshawar", value: "Peshawar" },
	{ label: "Islamabad", value: "Islamabad" },
	{ label: "Quetta", value: "Quetta" },
	{ label: "Sargodha", value: "Sargodha" },
	{ label: "Sialkot", value: "Sialkot" },
	{ label: "Bahawalpur", value: "Bahawalpur" },
	{ label: "Sukkur", value: "Sukkur" },
	{ label: "Kandhkot", value: "Kandhkot" },
	{ label: "Sheikhupura", value: "Sheikhupura" },
	{ label: "Mardan", value: "Mardan" },
	{ label: "Gujrat", value: "Gujrat" },
	{ label: "Larkana", value: "Larkana" },
	{ label: "Kasur", value: "Kasur" },
	{ label: "Rahim Yar Khan", value: "Rahim Yar Khan" },
	{ label: "Sahiwal", value: "Sahiwal" },
	{ label: "Okara", value: "Okara" },
	{ label: "Wah Cantonment", value: "Wah Cantonment" },
	{ label: "Dera Ghazi Khan", value: "Dera Ghazi Khan" },
	{ label: "Mingora", value: "Mingora" },
	{ label: "Mirpur Khas", value: "Mirpur Khas" },
	{ label: "Chiniot", value: "Chiniot" },
	{ label: "Nawabshah", value: "Nawabshah" },
	{ label: "Kamoke", value: "Kamoke" },
	{ label: "Burewala", value: "Burewala" },
	{ label: "Jhelum", value: "Jhelum" },
	{ label: "Sadiqabad", value: "Sadiqabad" },
	{ label: "Khanewal", value: "Khanewal" },
	{ label: "Hafizabad", value: "Hafizabad" },
	{ label: "Kohat", value: "Kohat" },
	{ label: "Jacobabad", value: "Jacobabad" },
	{ label: "Shikarpur", value: "Shikarpur" },
	{ label: "Muzaffargarh", value: "Muzaffargarh" },
	{ label: "Khanpur", value: "Khanpur" },
	{ label: "Gojra", value: "Gojra" },
	{ label: "Bahawalnagar", value: "Bahawalnagar" },
	{ label: "Abbottabad", value: "Abbottabad" },
	{ label: "Muridke", value: "Muridke" },
	{ label: "Pakpattan", value: "Pakpattan" },
	{ label: "Khuzdar", value: "Khuzdar" },
	{ label: "Jaranwala", value: "Jaranwala" },
	{ label: "Chishtian", value: "Chishtian" },
	{ label: "Daska", value: "Daska" },
	{ label: "Mandi Bahauddin", value: "Mandi Bahauddin" },
	{ label: "Ahmadpur East", value: "Ahmadpur East" },
	{ label: "Kamalia", value: "Kamalia" },
	{ label: "Tando Adam", value: "Tando Adam" },
	{ label: "Khairpur", value: "Khairpur" },
	{ label: "Dera Ismail Khan", value: "Dera Ismail Khan" },
	{ label: "Vehari", value: "Vehari" },
	{ label: "Nowshera", value: "Nowshera" },
	{ label: "Dadu", value: "Dadu" },
	{ label: "Wazirabad", value: "Wazirabad" },
	{ label: "Khushab", value: "Khushab" },
	{ label: "Charsada", value: "Charsada" },
	{ label: "Swabi", value: "Swabi" },
	{ label: "Chakwal", value: "Chakwal" },
	{ label: "Mianwali", value: "Mianwali" },
	{ label: "Tando Allahyar", value: "Tando Allahyar" },
	{ label: "Kot Adu", value: "Kot Adu" },
	{ label: "Farooka", value: "Farooka" },
	{ label: "Chichawatni", value: "Chichawatni" },
	{ label: "Mansehra", value: "Mansehra" },
];

export const institutes = [
	{ label: "Government/Public", value: "Government/Public" },
	{ label: "Private", value: "Private" },
	{ label: "Semi Private", value: "Semi Private" },
];

export const subjectGroups = [
	{ label: "Engineering", value: "Engineering" },
	{ label: "Medical", value: "Medical" },
	{ label: "Law", value: "Law" },
	{ label: "Arts", value: "Arts" },
	{ label: "Commerce", value: "Commerce" },
	{ label: "Science", value: "Science" },
	{ label: "Humanities", value: "Humanities" },
	{ label: "Computer Science", value: "Computer Science" },
	{ label: "Social Sciences", value: "Social Sciences" },
	{ label: "Other", value: "Other" },
];

export const targetEntryTests = [
	{ label: "NUST Entry Test (NET)", value: "NET" },
	{ label: "FAST", value: "FAST" },
	{ label: "GIKI", value: "GIKI" },
	{ label: "PIEAS", value: "PIEAS" },
	{ label: "PUCIT (USAT)", value: "USAT" },
	{ label: "ECAT", value: "ECAT" },
	{ label: "NAT", value: "NAT" },
	{ label: "SAT", value: "SAT" },
	{ label: "UHS", value: "UHS" },
	{ label: "MDCAT", value: "MDCAT" },
	{ label: "Other", value: "Other" },
];

// Filter constants for education background
export const chapters = [
	{ label: "Derivatives", value: "derivatives" },
	{ label: "Integration", value: "integration" },
	{ label: "Limits", value: "limits" },
	{ label: "Functions", value: "functions" },
	{ label: "Trigonometry", value: "trigonometry" },
	{ label: "Algebra", value: "algebra" },
	{ label: "Geometry", value: "geometry" },
	{ label: "Statistics", value: "statistics" },
];

export const topics = [
	{
		label: "Basic Formulas and Properties",
		value: "basic-formulas-properties",
	},
	{ label: "Advanced Concepts", value: "advanced-concepts" },
	{ label: "Problem Solving Techniques", value: "problem-solving" },
	{ label: "Applications", value: "applications" },
	{ label: "Theory and Definitions", value: "theory-definitions" },
	{ label: "Examples and Practice", value: "examples-practice" },
];

export const languages = [
	{ label: "English", value: "english" },
	{ label: "Urdu", value: "urdu" },
];

export const categories = [
	{ label: "FLPs", value: "flps" },
	{ label: "Lecture", value: "lecture" },
	{ label: "Short trick", value: "short-trick" },
	{ label: "Revision", value: "revision" },
	{ label: "Tips", value: "tips" },
	{ label: "1-Shot", value: "one-shot" },
	{ label: "Concepts", value: "concepts" },
	{ label: "Past Year Questions", value: "past-year-questions" },
	{ label: "Formula Sheet", value: "formula-sheet" },
];
