import { ICONS } from "@/lib/assets/images";
import { SheetMenu } from "./sidebar/sheet-menu";
import { UserNav } from "./sidebar/user-nav";
import Notifications from "./notification";
import SearchParhlai from "./search-parhlai";
import { routeList } from "@/lib/route-list";
import { useRouter } from "@tanstack/react-router";
import { ChevronLeft } from "react-feather";

// Routes that should show go back button instead of menu in mobile view
const GO_BACK_ROUTES = ["/"];

export function Navbar() {
	const { history } = useRouter();
	const pathname = window.location.pathname;
	const currentMenu = routeList
		.find((menu) => menu.menus.some((m) => m.href === pathname))
		?.menus.find((m) => m.href === pathname);
	const label = currentMenu?.label;

	// Check if current route should show go back button
	const shouldShowGoBack = GO_BACK_ROUTES.includes(pathname);

	const handleGoBack = () => {
		history.go(-1);
	};

	return (
		<>
			<header className="fixed top-0 z-50 w-full bg-background lg:bg-white">
				<div className="mx-4 sm:ml-6 sm:mr-10 flex h-[90px] items-center justify-between">
					{/* Mobile navigation - either go back button or sheet menu */}
					{shouldShowGoBack ? (
						<button
							onClick={handleGoBack}
							className="lg:hidden"
							aria-label="Go back"
						>
							<ChevronLeft size={24} />
						</button>
					) : (
						<SheetMenu />
					)}

					<div className="lg:flex items-center lg:space-x-0 mr-40 hidden">
						<img
							src={ICONS.logoexpanded}
							alt="Logo"
							className="w-24 sm:w-28 lg:w-32"
						/>
					</div>
					<h6 className="font-medium lg:hidden">{label}</h6>

					{/* Hide search bar when showing go back button in mobile */}
					{<SearchParhlai visibleInMobile={!shouldShowGoBack} />}

					<div className="lg:flex flex-1 gap-x-2 hidden sm:gap-x-12 items-center justify-end ">
						<div className="flex gap-x-4 ">
							<Notifications />
						</div>
						<UserNav />
					</div>
				</div>
			</header>
			<div className="h-[90px]" />
		</>
	);
}
