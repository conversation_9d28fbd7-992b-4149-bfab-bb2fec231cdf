import { useState, useRef, useEffect } from "react";
import { useNavigate } from "@tanstack/react-router";
import { Input } from "@/components/ui/input";
import { useMediaQuery } from "react-responsive";
import { Search } from "react-feather";
import clsx from "clsx";
import {
	fuzzySearch,
	searchableItems,
	SearchableItem,
} from "@/lib/search-data";
import SearchResults from "@/components/common/search-results";
import MobileSearchDialog from "@/components/common/mobile-search-dialog";

interface SearchParhlaiProps {
	visibleInMobile?: boolean;
	disabled?: boolean;
}

const SearchParhlai: React.FC<SearchParhlaiProps> = ({
	visibleInMobile = true,
	disabled = false,
}) => {
	const [searchQuery, setSearchQuery] = useState("");
	const [searchResults, setSearchResults] = useState<SearchableItem[]>([]);
	const [showResults, setShowResults] = useState(false);
	const [showMobileSearch, setShowMobileSearch] = useState(false);
	const navigate = useNavigate();
	const searchRef = useRef<HTMLDivElement>(null);
	const isDesktop = useMediaQuery({ minWidth: 1024 });

	// Handle search input changes
	const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const query = e.target.value;
		setSearchQuery(query);

		if (query.trim()) {
			const results = fuzzySearch(query, searchableItems);
			setSearchResults(results);
			setShowResults(true);
		} else {
			setSearchResults([]);
			setShowResults(false);
		}
	};

	// Handle keyboard events
	const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
		if (e.key === "Enter" && searchResults.length > 0 && searchResults[0]) {
			// Navigate to first result on Enter
			navigate({ to: searchResults[0].href });
			handleCloseSearch();
		} else if (e.key === "Escape") {
			handleCloseSearch();
		}
	};

	// Close search results
	const handleCloseSearch = () => {
		setShowResults(false);
		setSearchQuery("");
		setSearchResults([]);
	};

	// Handle mobile search click
	const handleMobileSearchClick = () => {
		if (!disabled) {
			setShowMobileSearch(true);
		}
	};

	// Handle click outside to close search results
	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (
				searchRef.current &&
				!searchRef.current.contains(event.target as Node)
			) {
				setShowResults(false);
			}
		};

		document.addEventListener("mousedown", handleClickOutside);
		return () => {
			document.removeEventListener("mousedown", handleClickOutside);
		};
	}, []);

	if (isDesktop)
		return (
			<div className="flex max-w-96 flex-1 gap-x-3 relative" ref={searchRef}>
				<Input
					startIcon={Search}
					placeholder="Search features, pages..."
					disabled={disabled}
					value={searchQuery}
					onChange={handleSearchChange}
					onKeyDown={handleKeyDown}
					className={clsx(
						"sans h-14 rounded-[10px] items-center",
						"placeholder:text-gray-400 text-gray-600",
						{
							"opacity-50 cursor-not-allowed": disabled,
						}
					)}
				/>
				{showResults && !disabled && (
					<SearchResults
						results={searchResults}
						onItemClick={handleCloseSearch}
					/>
				)}
			</div>
		);
	else
		return (
			<>
				<button
					onClick={handleMobileSearchClick}
					className={clsx(
						visibleInMobile ? "lg:hidden" : "invisible",
						"p-2 hover:bg-gray-100 rounded-lg transition-colors",
						{
							"opacity-50 cursor-not-allowed": disabled,
						}
					)}
					disabled={disabled}
				>
					<Search size={24} />
				</button>
				<MobileSearchDialog
					open={showMobileSearch}
					onOpenChange={setShowMobileSearch}
				/>
			</>
		);
};

export default SearchParhlai;
