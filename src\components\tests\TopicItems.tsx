import * as React from "react";
import { CircleDot } from "lucide-react";

import AddVariantButton from "@/components/tests/AddVariant";
import {
	MCQTypeDropdown,
	DifficultyDropdown,
} from "@/components/tests/MenuDropdown";
import QuestionsInput from "@/components/tests/QuestionsInput";

import type { SelectedTopic, TopicFormData } from "@/features/tests/custom";

type ComboMaps = Record<string, Record<string, number>>;
const norm = (s: string) => (s ?? "").trim().toLowerCase();

const ensureForm = (f?: TopicFormData): TopicFormData => ({
	numQuestions: f?.numQuestions ?? "",
	testType: f?.testType ?? "",
	difficulty: f?.difficulty ?? "",
});

type TopicItemsProps = {
	mode: "custom" | "subject";
	subjectId: string;
	topics: string[];
	searchTerm?: string;

	selectedTopics: SelectedTopic[];
	setSelectedTopics: React.Dispatch<React.SetStateAction<SelectedTopic[]>>;
	comboMaps: ComboMaps;
	setComboMaps: React.Dispatch<React.SetStateAction<ComboMaps>>;
	topicFormData: Record<string, TopicFormData>;
	setTopicFormData: React.Dispatch<
		React.SetStateAction<Record<string, TopicFormData>>
	>;

	isPairAlreadySelected: (
		subjectId: string,
		topic: string,
		type: string,
		difficulty: string
	) => boolean;
	getSelectedPairsSet: (subjectId: string, topic: string) => Set<string>;
	getAllTypesForTopic: (subjectId: string, topic: string) => string[];
	getAllDiffsForTopic: (subjectId: string, topic: string) => string[];
	getTypesForTopicHavingDifficulty: (
		subjectId: string,
		topic: string,
		difficulty: string
	) => string[];
	getDiffsForTopicType: (
		subjectId: string,
		topic: string,
		type: string
	) => string[];
	getMaxForPair: (
		subjectId: string,
		topic: string,
		type: string,
		difficulty: string
	) => number;

	totalSelectedMCQs: number;

	subjectDefaults?: {
		testType: string;
		difficulty: string;
		numQuestions: string;
	};

	mobileTopRightCount?: { value: number; label?: string };

	className?: string;
};

const TopicItems: React.FC<TopicItemsProps> = ({
	mode,
	subjectId,
	topics,
	searchTerm = "",
	selectedTopics,
	setSelectedTopics,
	comboMaps,
	setComboMaps,
	topicFormData,
	setTopicFormData,
	isPairAlreadySelected,
	getSelectedPairsSet,
	getAllTypesForTopic,
	getAllDiffsForTopic,
	getTypesForTopicHavingDifficulty,
	getDiffsForTopicType,
	getMaxForPair,
	totalSelectedMCQs,
	subjectDefaults,
	mobileTopRightCount,
	className = "",
}) => {
	//  single controller for all dropdowns in this list
	const [openDropdownId, setOpenDropdownId] = React.useState<string | null>(
		null
	);
	React.useEffect(() => {
		const onKey = (e: KeyboardEvent) => {
			if (e.key === "Escape") setOpenDropdownId(null);
		};
		window.addEventListener("keydown", onKey);
		return () => window.removeEventListener("keydown", onKey);
	}, []);

	const updateTopicFormData = (
		topicKey: string,
		field: keyof TopicFormData,
		value: string
	) => {
		setTopicFormData((prev) => {
			const next = { ...prev };
			const prevFull = ensureForm(next[topicKey]);
			next[topicKey] = { ...prevFull, [field]: value };
			return next;
		});
	};

	return (
		<div
			className={`relative overflow-y-auto overflow-x-hidden pr-1 ${className}`}
		>
			{mobileTopRightCount && (
				<div className="sm:hidden absolute -top-6 right-0 text-xs text-gray-500">
					{mobileTopRightCount.value} {mobileTopRightCount.label ?? "topic(s)"}
				</div>
			)}

			{searchTerm && topics.length === 0 ? (
				<div
					className="p-4 text-center text-gray-500 border border-gray-200 rounded-lg"
					style={{ fontFamily: "Inter, sans-serif", fontWeight: 400 }}
				>
					No topics found matching "{searchTerm}"
				</div>
			) : null}

			<div className="space-y-3">
				{topics.map((topic) => {
					const topicKey = `${subjectId}-${topic}`;
					const baseForm = ensureForm(topicFormData[topicKey]);

					// ------- SUBJECT MODE (fixed pair + count coming from subject-level input) -------
					const lockedType = (subjectDefaults?.testType ?? "").toString(); // don’t change UI
					const lockedDiff = (subjectDefaults?.difficulty ?? "").toString(); // don’t change UI
					// In subject mode, do NOT auto-fill to 1; require explicit input
					const subjectCount = baseForm.numQuestions ?? "";

					const formData: TopicFormData =
						mode === "subject"
							? {
									numQuestions: subjectCount, // ← carry to AddVariantButton
									testType: lockedType, // ← fixed pair for subject tests
									difficulty: lockedDiff,
								}
							: baseForm;

					let typeOptions: string[] = [];
					let difficultyOptions: string[] = [];

					if (mode === "custom") {
						typeOptions = formData.difficulty
							? getTypesForTopicHavingDifficulty(
									subjectId,
									topic,
									formData.difficulty
								)
							: getAllTypesForTopic(subjectId, topic);

						difficultyOptions = formData.testType
							? getDiffsForTopicType(subjectId, topic, formData.testType)
							: getAllDiffsForTopic(subjectId, topic);

						const selectedPairs = getSelectedPairsSet(subjectId, topic);
						typeOptions = typeOptions.filter(
							(ty) =>
								!selectedPairs.has(
									`${norm(ty)}__${norm(formData.difficulty || "")}`
								)
						);
						difficultyOptions = difficultyOptions.filter(
							(df) =>
								!selectedPairs.has(
									`${norm(formData.testType || "")}__${norm(df)}`
								)
						);
					}

					const typeId = `type:${subjectId}:${topic}`;
					const diffId = `diff:${subjectId}:${topic}`;

					// max label removed in subject mode; per-topic max is enforced by QuestionsInput

					return (
						<div
							key={topicKey}
							className="bg-white sm:rounded-lg sm:border sm:border-gray-200"
						>
							{/* Desktop layout */}
							<div className="hidden sm:grid grid-cols-[minmax(0,1.2fr)_auto] items-center gap-3 p-4">
								<div className="flex items-center gap-3 min-w-0 pr-2 sm:pr-4">
									<CircleDot className="w-3 h-3 text-gray-400 flex-shrink-0 fill-current" />
									<span
										className="text-gray-800 min-w-0 break-words"
										style={{
											fontFamily: "Inter, sans-serif",
											fontWeight: 400,
											fontSize: "16px",
										}}
									>
										{topic}
									</span>
								</div>

								<div className="flex items-center gap-3 min-w-0 md:min-w-max flex-wrap md:flex-nowrap">
									{/* In subject mode, show No. of questions input next to Add */}
									{mode === "subject" && (
										<div className="shrink-0 w-44 sm:w-40 mr-2">
											<QuestionsInput
												mode={mode}
												subjectId={subjectId}
												topicName={topic}
												topicKey={topicKey}
												formData={formData}
												setTopicFormData={setTopicFormData}
												selectedTopics={selectedTopics}
												comboMaps={comboMaps}
												cap={200}
											/>
										</div>
									)}

									<AddVariantButton
										mode={mode}
										className="flex-shrink-0"
										subjectId={subjectId}
										topicName={topic}
										topicKey={topicKey}
										formData={formData} // ← now includes correct num/type/diff
										selectedTopics={selectedTopics}
										setSelectedTopics={setSelectedTopics}
										comboMaps={comboMaps}
										setComboMaps={setComboMaps}
										setTopicFormData={setTopicFormData}
										cap={200}
									/>

									{mode === "custom" && (
										<>
											<div className="flex items-center gap-3">
												<MCQTypeDropdown
													value={formData.testType}
													options={typeOptions}
													onChange={(value) => {
														updateTopicFormData(topicKey, "testType", value);

														if (
															formData.difficulty &&
															isPairAlreadySelected(
																subjectId,
																topic,
																value,
																formData.difficulty
															)
														) {
															updateTopicFormData(topicKey, "difficulty", "");
														}

														if (value && formData.difficulty) {
															const maxForPairLocal = getMaxForPair(
																subjectId,
																topic,
																value,
																formData.difficulty
															);
															const remainingGlobal = Math.max(
																0,
																200 - totalSelectedMCQs
															);
															const effectiveMaxLocal = Math.min(
																maxForPairLocal,
																remainingGlobal
															);
															const num =
																parseInt(formData.numQuestions || "0", 10) || 0;
															if (num > effectiveMaxLocal) {
																updateTopicFormData(
																	topicKey,
																	"numQuestions",
																	effectiveMaxLocal > 0
																		? String(effectiveMaxLocal)
																		: ""
																);
															}
														}
													}}
													open={openDropdownId === typeId}
													onOpenChange={(next) =>
														setOpenDropdownId(next ? typeId : null)
													}
												/>

												<div className="h-5 w-px bg-gray-300" />

												<DifficultyDropdown
													value={formData.difficulty}
													options={difficultyOptions}
													onChange={(value) => {
														updateTopicFormData(topicKey, "difficulty", value);

														if (
															formData.testType &&
															isPairAlreadySelected(
																subjectId,
																topic,
																formData.testType,
																value
															)
														) {
															updateTopicFormData(topicKey, "testType", "");
														}

														if (formData.testType && value) {
															const maxForPairLocal = getMaxForPair(
																subjectId,
																topic,
																formData.testType,
																value
															);
															const remainingGlobal = Math.max(
																0,
																200 - totalSelectedMCQs
															);
															const effectiveMaxLocal = Math.min(
																maxForPairLocal,
																remainingGlobal
															);
															const num =
																parseInt(formData.numQuestions || "0", 10) || 0;
															if (num > effectiveMaxLocal) {
																updateTopicFormData(
																	topicKey,
																	"numQuestions",
																	effectiveMaxLocal > 0
																		? String(effectiveMaxLocal)
																		: ""
																);
															}
														}
													}}
													open={openDropdownId === diffId}
													onOpenChange={(next) =>
														setOpenDropdownId(next ? diffId : null)
													}
												/>
											</div>

											<div className="h-5 w-px bg-gray-300" />

											<div className="shrink-0 w-44 sm:w-40">
												<QuestionsInput
													subjectId={subjectId}
													topicName={topic}
													topicKey={topicKey}
													formData={formData}
													setTopicFormData={setTopicFormData}
													selectedTopics={selectedTopics}
													comboMaps={comboMaps}
													cap={200}
												/>
											</div>
										</>
									)}
								</div>
							</div>

							{/* Mobile Layout  */}
							<div className="sm:hidden px-4 py-3 space-y-4">
								<div className="flex items-start gap-2 min-w-0">
									<span className="inline-block w-1.5 h-1.5 rounded-full bg-gray-400 flex-shrink-0 mt-2" />
									<span
										className="text-[15px] leading-5 text-gray-900 font-medium break-words flex-1"
										style={{
											fontFamily: "Inter, sans-serif",
											wordBreak: "break-word",
											overflowWrap: "break-word",
										}}
									>
										{topic}
									</span>
								</div>

								{mode === "subject" && (
					<div className="pl-4">
										<div
											className="relative z-10 inline-block"
											onPointerDown={(e) => e.stopPropagation()}
											onMouseDown={(e) => e.stopPropagation()}
											onClick={(e) => e.stopPropagation()}
										>
						<div className="flex items-center gap-3">
												<div className="w-[180px]">
													<QuestionsInput
														mode={mode}
														subjectId={subjectId}
														topicName={topic}
														topicKey={topicKey}
														formData={formData}
														setTopicFormData={setTopicFormData}
														selectedTopics={selectedTopics}
														comboMaps={comboMaps}
														cap={200}
													/>
												</div>
												<AddVariantButton
													mode={mode}
													className="flex-shrink-0 text-[#5936cd] hover:text-[#4c2dae]"
													subjectId={subjectId}
													topicName={topic}
													topicKey={topicKey}
													formData={formData}
													selectedTopics={selectedTopics}
													setSelectedTopics={setSelectedTopics}
													comboMaps={comboMaps}
													setComboMaps={setComboMaps}
													setTopicFormData={setTopicFormData}
													cap={200}
												/>
											</div>
										</div>
									</div>
								)}

								{/* Add Variant Button */}
								<div className="pl-4">
									<div
										className="relative z-10 inline-block"
										onPointerDown={(e) => e.stopPropagation()}
										onMouseDown={(e) => e.stopPropagation()}
										onClick={(e) => e.stopPropagation()}
									>
										{mode === "custom" && (
											<AddVariantButton
												mode={mode}
												className="flex-shrink-0 text-[#5936cd] hover:text-[#4c2dae]"
												subjectId={subjectId}
												topicName={topic}
												topicKey={topicKey}
												formData={formData}
												selectedTopics={selectedTopics}
												setSelectedTopics={setSelectedTopics}
												comboMaps={comboMaps}
												setComboMaps={setComboMaps}
												setTopicFormData={setTopicFormData}
												cap={200}
											/>
										)}
									</div>
								</div>

								{/* Controls Section */}
								{mode === "custom" && (
									<div className="pl-4 space-y-3">
										{/* MCQ Type */}
										<div className="w-full max-w-[280px]">
											<MCQTypeDropdown
												value={formData.testType}
												options={typeOptions}
												onChange={(value) => {
													updateTopicFormData(topicKey, "testType", value);

													if (
														formData.difficulty &&
														isPairAlreadySelected(
															subjectId,
															topic,
															value,
															formData.difficulty
														)
													) {
														updateTopicFormData(topicKey, "difficulty", "");
													}

													if (value && formData.difficulty) {
														const maxForPairLocal = getMaxForPair(
															subjectId,
															topic,
															value,
															formData.difficulty
														);
														const remainingGlobal = Math.max(
															0,
															200 - totalSelectedMCQs
														);
														const effectiveMaxLocal = Math.min(
															maxForPairLocal,
															remainingGlobal
														);
														const num =
															parseInt(formData.numQuestions || "0", 10) || 0;
														if (num > effectiveMaxLocal) {
															updateTopicFormData(
																topicKey,
																"numQuestions",
																effectiveMaxLocal > 0
																	? String(effectiveMaxLocal)
																	: ""
															);
														}
													}
												}}
												open={openDropdownId === typeId}
												onOpenChange={(next) =>
													setOpenDropdownId(next ? typeId : null)
												}
											/>
										</div>

										{/* Difficulty and Questions */}
										<div className="grid grid-cols-1 gap-4">
											<div className="w-full max-w=[280px]">
												<DifficultyDropdown
													className="w-full"
													value={formData.difficulty}
													options={difficultyOptions}
													onChange={(value) => {
														updateTopicFormData(topicKey, "difficulty", value);
														if (
															formData.testType &&
															isPairAlreadySelected(
																subjectId,
																topic,
																formData.testType,
																value
															)
														) {
															updateTopicFormData(topicKey, "testType", "");
														}
														if (formData.testType && value) {
															const maxForPairLocal = getMaxForPair(
																subjectId,
																topic,
																formData.testType,
																value
															);
															const remainingGlobal = Math.max(
																0,
																200 - totalSelectedMCQs
															);
															const effectiveMaxLocal = Math.min(
																maxForPairLocal,
																remainingGlobal
															);
															const num =
																parseInt(formData.numQuestions || "0", 10) || 0;
															if (num > effectiveMaxLocal) {
																updateTopicFormData(
																	topicKey,
																	"numQuestions",
																	effectiveMaxLocal > 0
																		? String(effectiveMaxLocal)
																		: ""
																);
															}
														}
													}}
													open={openDropdownId === diffId}
													onOpenChange={(next) =>
														setOpenDropdownId(next ? diffId : null)
													}
												/>
											</div>

											<div className="w-full max-w-[280px]">
												<QuestionsInput
													subjectId={subjectId}
													topicName={topic}
													topicKey={topicKey}
													formData={formData}
													setTopicFormData={setTopicFormData}
													selectedTopics={selectedTopics}
													comboMaps={comboMaps}
													cap={200}
													className="w-full"
												/>
											</div>
										</div>
									</div>
								)}
							</div>
						</div>
					);
				})}
			</div>
		</div>
	);
};

export default TopicItems;
