import {
	<PERSON><PERSON><PERSON>,
	Bar<PERSON>hart2,
	Book,
	Clipboard,
	Paperclip,
	FileText,
	Zap,
	User,
	HelpCircle,
	Key,
	Clock,
	type Icon,
} from "react-feather";

export interface SearchableItem {
	id: string;
	title: string;
	description: string;
	href: string;
	icon: Icon;
	category: string;
	keywords: string[]; // Additional search terms
}

export const searchableItems: SearchableItem[] = [
	// Main Dashboard & Analytics
	{
		id: "dashboard",
		title: "Dashboard",
		description: "View your learning overview and progress",
		href: "/dashboard",
		icon: BookOpen,
		category: "Main",
		keywords: ["home", "overview", "progress", "stats", "main"],
	},
	{
		id: "analytics",
		title: "Analytics",
		description: "Detailed performance analytics and insights",
		href: "/analytics",
		icon: BarChart2,
		category: "Main",
		keywords: ["performance", "stats", "insights", "charts", "reports", "data"],
	},

	// Learning & Resources
	{
		id: "resources",
		title: "Resources",
		description: "Browse learning materials and study resources",
		href: "/learn",
		icon: Book,
		category: "Learning",
		keywords: ["learn", "materials", "study", "content", "chapters", "books"],
	},

	// Tests
	{
		id: "mock-test",
		title: "Mock Test",
		description: "Take practice tests to simulate real exams",
		href: "/t/mock",
		icon: Clipboard,
		category: "Tests",
		keywords: ["practice", "exam", "simulation", "test", "mock", "assessment"],
	},
	{
		id: "custom-test",
		title: "Custom Test",
		description: "Create personalized tests with your preferences",
		href: "/t/custom",
		icon: Paperclip,
		category: "Tests",
		keywords: ["personalized", "custom", "create", "build", "preferences"],
	},
	{
		id: "subject-test",
		title: "Subject Test",
		description: "Take tests focused on specific subjects",
		href: "/t/subject",
		icon: FileText,
		category: "Tests",
		keywords: ["subject", "specific", "focused", "topic", "area"],
	},
	{
		id: "ai-test",
		title: "AI Test",
		description: "AI-powered adaptive testing experience",
		href: "/t/ai",
		icon: Zap,
		category: "Tests",
		keywords: [
			"artificial intelligence",
			"adaptive",
			"smart",
			"ai",
			"intelligent",
		],
	},

	// Account & Settings
	{
		id: "account",
		title: "Account",
		description: "Manage your profile and account settings",
		href: "/account",
		icon: User,
		category: "Account",
		keywords: ["profile", "settings", "personal", "info", "manage"],
	},
	{
		id: "groq-key",
		title: "Groq API Key",
		description: "Configure your Groq API key for AI features",
		href: "/add-key", // Assuming it's in account settings
		icon: Key,
		category: "Account",
		keywords: ["api", "key", "groq", "ai", "configuration", "setup"],
	},

	// History & Help
	{
		id: "history",
		title: "Test History",
		description: "View your past test attempts and results",
		href: "/analytics", // Assuming history is part of analytics
		icon: Clock,
		category: "History",
		keywords: ["past", "previous", "attempts", "results", "records"],
	},
	{
		id: "help",
		title: "Help & Support",
		description: "Get help and contact support",
		href: "/help",
		icon: HelpCircle,
		category: "Support",
		keywords: ["support", "contact", "assistance", "faq", "guide"],
	},
];

// Simple fuzzy search function
export function fuzzySearch(
	query: string,
	items: SearchableItem[]
): SearchableItem[] {
	if (!query.trim()) return [];

	const queryLower = query.toLowerCase();

	const scored = items.map((item) => {
		let score = 0;
		const titleLower = item.title.toLowerCase();
		const descriptionLower = item.description.toLowerCase();
		const keywordsLower = item.keywords.join(" ").toLowerCase();
		const categoryLower = item.category.toLowerCase();

		// Exact title match gets highest score
		if (titleLower === queryLower) score += 100;
		// Title starts with query
		else if (titleLower.startsWith(queryLower)) score += 80;
		// Title contains query
		else if (titleLower.includes(queryLower)) score += 60;

		// Description matches
		if (descriptionLower.includes(queryLower)) score += 40;

		// Keywords match
		if (keywordsLower.includes(queryLower)) score += 30;

		// Category matches
		if (categoryLower.includes(queryLower)) score += 20;

		// Partial character matching (fuzzy)
		const titleChars = titleLower.split("");
		const queryChars = queryLower.split("");
		let fuzzyScore = 0;
		let queryIndex = 0;

		for (const char of titleChars) {
			if (queryIndex < queryChars.length && char === queryChars[queryIndex]) {
				fuzzyScore += 1;
				queryIndex++;
			}
		}

		if (queryIndex === queryChars.length) {
			score += fuzzyScore * 2; // Bonus for matching all characters in order
		}

		return { item, score };
	});

	// Filter out items with no score and sort by score
	return scored
		.filter(({ score }) => score > 0)
		.sort((a, b) => b.score - a.score)
		.map(({ item }) => item)
		.slice(0, 8); // Limit to top 8 results
}
