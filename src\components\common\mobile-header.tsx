import { routeList } from "@/lib/route-list";
import React from "react";

type MobileHeaderProps = {
	title?: string;
	resourceType?: string;
	rightSide?: React.ReactNode;
};

const MobileHeader = ({
	title,
	resourceType,
	rightSide: RightSide,
}: MobileHeaderProps) => {
	const pathname = window.location.pathname;
	const currentRoute = routeList
		.find((menu) => menu.menus.some((m) => m.href === pathname))
		?.menus.find((m) => m.href === pathname);

	const label = currentRoute?.label;

	return (
		<div className="fixed w-full flex items-center justify-between bg-background h-[80px] px-4">
			<h6 className="font-medium lg:hidden">
				{resourceType === "video"
					? "Videos"
					: resourceType === "files"
						? "Files"
						: resourceType === "link"
							? "Links"
							: (title ?? label)}
			</h6>
			{RightSide ?? <div />}
		</div>
	);
};

export default MobileHeader;
