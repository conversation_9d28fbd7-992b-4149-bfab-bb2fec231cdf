import { Button } from "@/components/ui/button";
import { Instagram, ExternalLink } from "react-feather";

interface NotificationsDropdownProps {
	onClose: () => void;
}

// Hardcoded notifications (same as dialog)
const notifications = [
	{
		id: 1,
		title: "Need guidance? Follow and message us on Instagram",
		description:
			"Get personalized study tips and stay updated with the latest content.",
		action: {
			label: "Follow on Instagram",
			url: "https://www.instagram.com/parhlai",
			icon: Instagram,
		},
		timestamp: new Date().toISOString(),
	},
];

const NotificationsDropdown = ({ onClose }: NotificationsDropdownProps) => {
	const handleNotificationClick = (url: string) => {
		window.open(url, "_blank");
		onClose(); // Close dropdown after action
	};

	return (
		<div className="absolute top-full right-0 bg-white border border-gray-200 rounded-lg shadow-lg z-50 mt-1 w-80 max-h-96 overflow-y-auto">
			<div className="p-3 border-b border-gray-100">
				<div className="flex items-center gap-2">
					<h3 className="text-sm font-medium text-gray-900">Notifications</h3>
					{notifications.length > 0 && (
						<span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full">
							{notifications.length}
						</span>
					)}
				</div>
			</div>

			<div className="max-h-80 overflow-y-auto">
				{notifications.length > 0 ? (
					<div className="p-2">
						{notifications.map((notification) => (
							<div
								key={notification.id}
								className="border border-gray-100 rounded-lg p-3 space-y-2 hover:bg-gray-50 transition-colors"
							>
								<div>
									<h4 className="text-sm font-medium text-gray-900 leading-tight">
										{notification.title}
									</h4>
									<p className="text-xs text-gray-600 mt-1 leading-relaxed">
										{notification.description}
									</p>
								</div>
								<Button
									onClick={() =>
										handleNotificationClick(notification.action.url)
									}
									className="w-full bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white"
									size="sm"
								>
									<notification.action.icon size={14} className="mr-2" />
									{notification.action.label}
									<ExternalLink size={12} className="ml-2" />
								</Button>
							</div>
						))}
					</div>
				) : (
					<div className="p-4 text-center text-gray-500">
						<p className="text-sm">No new notifications</p>
						<p className="text-xs">You're all caught up!</p>
					</div>
				)}
			</div>
		</div>
	);
};

export default NotificationsDropdown;
