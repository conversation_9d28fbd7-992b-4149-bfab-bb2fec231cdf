import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>ontent,
	<PERSON><PERSON><PERSON>eader,
	DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Instagram, ExternalLink } from "react-feather";

interface NotificationsDialogProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
}

// Hardcoded notifications
const notifications = [
	{
		id: 1,
		title: "Need guidance? Follow and message us on Instagram",
		description:
			"Get personalized study tips and stay updated with the latest content.",
		action: {
			label: "Follow on Instagram",
			url: "https://www.instagram.com/parhlai",
			icon: Instagram,
		},
		timestamp: new Date().toISOString(),
	},
];

const NotificationsDialog = ({
	open,
	onOpenChange,
}: NotificationsDialogProps) => {
	const handleNotificationClick = (url: string) => {
		window.open(url, "_blank");
	};

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="sm:max-w-md">
				<DialogHeader>
					<DialogTitle className="flex items-center gap-2">
						<span>Notifications</span>
						{notifications.length > 0 && (
							<span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full">
								{notifications.length}
							</span>
						)}
					</DialogTitle>
				</DialogHeader>
				<div className="space-y-4">
					{notifications.length > 0 ? (
						notifications.map((notification) => (
							<div
								key={notification.id}
								className="border rounded-lg p-4 space-y-3 hover:bg-gray-50 transition-colors"
							>
								<div>
									<h3 className="font-medium text-sm">{notification.title}</h3>
									<p className="text-sm text-gray-600 mt-1">
										{notification.description}
									</p>
								</div>
								<Button
									onClick={() =>
										handleNotificationClick(notification.action.url)
									}
									className="w-full bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white"
									size="sm"
								>
									<notification.action.icon size={16} className="mr-2" />
									{notification.action.label}
									<ExternalLink size={14} className="ml-2" />
								</Button>
							</div>
						))
					) : (
						<div className="text-center py-8 text-gray-500">
							<p>No notifications at the moment</p>
						</div>
					)}
				</div>
			</DialogContent>
		</Dialog>
	);
};

export default NotificationsDialog;
