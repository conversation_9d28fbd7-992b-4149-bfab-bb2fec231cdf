import * as React from "react";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import type { SelectedTopic, TopicFormData } from "@/features/tests/custom";

type ComboMaps = Record<string, Record<string, number>>;

const norm = (s: string) => (s ?? "").trim().toLowerCase();

function getMaxForPair(
	comboMaps: ComboMaps,
	subjectId: string,
	topic: string,
	type: string,
	difficulty: string
): number {
	const cm = comboMaps[subjectId] || {};
	const k = `${topic.toLowerCase()}__${type.toLowerCase()}__${difficulty.toLowerCase()}`;
	return cm[k] ?? 0;
}

function isPairAlreadySelected(
	selected: SelectedTopic[],
	subjectId: string,
	topic: string,
	type: string,
	difficulty: string
): boolean {
	return selected.some(
		(t) =>
			norm(t.subjectId) === norm(subjectId) &&
			norm(t.name) === norm(topic) &&
			norm(t.testType) === norm(type) &&
			norm(t.difficulty) === norm(difficulty)
	);
}

export type AddVariantButtonProps = {
	/** Distinguish validation/lookup behaviour */
	mode: "custom" | "subject";

	subjectId: string;
	topicName: string;
	topicKey: string;

	formData: TopicFormData;

	selectedTopics: SelectedTopic[];
	setSelectedTopics: React.Dispatch<React.SetStateAction<SelectedTopic[]>>;

	comboMaps: ComboMaps;
	setComboMaps: React.Dispatch<React.SetStateAction<ComboMaps>>;

	setTopicFormData: React.Dispatch<
		React.SetStateAction<Record<string, TopicFormData>>
	>;

	cap?: number;
	className?: string;
	label?: React.ReactNode;
	onAdded?: (newTopic: SelectedTopic) => void;
};

const AddVariantButton: React.FC<AddVariantButtonProps> = ({
	mode,
	subjectId,
	topicName,
	topicKey,
	formData,
	selectedTopics,
	setSelectedTopics,
	comboMaps,
	setComboMaps,
	setTopicFormData,
	cap = 200,
	className = "",
	label = "+ Add to test",
	onAdded,
}) => {
	const { toast } = useToast();

	const totalSelectedMCQs = React.useMemo(
		() =>
			selectedTopics.reduce(
				(acc, t) => acc + (parseInt(t.numQuestions, 10) || 0),
				0
			),
		[selectedTopics]
	);

	const requireTypeDifficulty = mode === "custom";

	const effectiveType = requireTypeDifficulty ? formData.testType || "" : "";
	const effectiveDiff = requireTypeDifficulty ? formData.difficulty || "" : "";

	let pairMax = 0;
	if (mode === "subject") {
		pairMax = getMaxForPair(comboMaps, subjectId, topicName, "", "");
	} else if (effectiveType && effectiveDiff) {
		pairMax = getMaxForPair(
			comboMaps,
			subjectId,
			topicName,
			effectiveType,
			effectiveDiff
		);
	}

	const remainingGlobal = Math.max(0, cap - totalSelectedMCQs);
	const effectiveMax = Math.min(pairMax, remainingGlobal);
	const requested = parseInt(formData.numQuestions || "0", 10);

	const canAdd =
		(!requireTypeDifficulty || (!!effectiveType && !!effectiveDiff)) &&
		requested > 0 &&
		effectiveMax > 0 &&
		requested <= effectiveMax;

	const title = canAdd
		? "Add to test"
		: requireTypeDifficulty && (!effectiveType || !effectiveDiff)
			? "Select type and difficulty"
			: pairMax > 0
				? `Enter 1–${Math.min(pairMax, remainingGlobal)}`
				: "Not enough MCQs";

	const enabledPurpleClasses =
		"border-[#5936cd] text-[#5936cd] hover:bg-[#5936cd]/10";

	const handleClick = () => {
		const req = parseInt(formData.numQuestions || "0", 10) || 0;

		if (requireTypeDifficulty && (!effectiveType || !effectiveDiff)) {
			toast({
				title: "Error",
				description: "Please select type and difficulty.",
				variant: "destructive",
			});
			return;
		}

		const currentTotal = selectedTopics.reduce(
			(acc, t) => acc + (parseInt(t.numQuestions, 10) || 0),
			0
		);
		const remaining = Math.max(0, cap - currentTotal);
		if (remaining <= 0) {
			toast({
				title: "Limit reached",
				description: `You have reached the maximum of ${cap} MCQs for this test.`,
				variant: "destructive",
			});
			return;
		}

		const effMax = Math.min(pairMax, remaining);
		if (req <= 0) {
			toast({
				title: "Error",
				description: "Count must be a positive number.",
				variant: "destructive",
			});
			return;
		}
		if (req > effMax) {
			toast({
				title: "Not enough MCQs",
				description: `Requested ${req}, but only ${effMax} available for ${topicName}${
					requireTypeDifficulty ? ` (${effectiveType}, ${effectiveDiff})` : ""
				}.`,
				variant: "destructive",
			});
			return;
		}

		if (
			isPairAlreadySelected(
				selectedTopics,
				subjectId,
				topicName,
				effectiveType,
				effectiveDiff
			)
		) {
			toast({
				title: "Duplicate combination",
				description: `"${topicName}"${
					requireTypeDifficulty ? ` (${effectiveType}, ${effectiveDiff})` : ""
				} is already added.`,
				variant: "destructive",
			});
			return;
		}

		const newTopic: SelectedTopic = {
			id: `${subjectId}-${topicName}-${Date.now()}`,
			subjectId,
			name: topicName,
			numQuestions: String(req),
			testType: effectiveType,
			difficulty: effectiveDiff,
		};

		setSelectedTopics((prev) => [...prev, newTopic]);

		setComboMaps((prev) => {
			const next = { ...prev };
			const cm = { ...(next[subjectId] || {}) };
			const k = `${topicName.toLowerCase()}__${effectiveType.toLowerCase()}__${effectiveDiff.toLowerCase()}`;
			const remainingForPair = Math.max(0, (cm[k] ?? 0) - req);
			cm[k] = remainingForPair;
			next[subjectId] = cm;
			return next;
		});

		setTopicFormData((prev) => {
			const next = { ...prev };
			delete next[topicKey];
			return next;
		});

		toast({ title: "Success", description: "Topic added successfully!" });

		const remainingForPairAfter = pairMax - req;
		if (remainingForPairAfter <= 0) {
			toast({
				title: "Combination exhausted",
				description: `"${topicName}"${
					requireTypeDifficulty ? ` (${effectiveType}, ${effectiveDiff})` : ""
				} is now fully used up for this test.`,
			});
		}

		onAdded?.(newTopic);
	};

	return (
		<Button
			variant="outline"
			size="sm"
			className={`${className ?? ""} ${canAdd ? enabledPurpleClasses : ""}`}
			disabled={!canAdd}
			onClick={handleClick}
			title={title}
		>
			{label}
		</Button>
	);
};

export default AddVariantButton;
