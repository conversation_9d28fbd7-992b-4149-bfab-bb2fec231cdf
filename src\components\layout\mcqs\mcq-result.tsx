import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>eader,
	<PERSON><PERSON><PERSON><PERSON>le,
	DialogDescription,
	DialogFooter,
} from "@/components/ui/dialog";
import { MCQ } from "@/features/mcqs/types";

type MCQResultProps = {
	open: boolean;
	selectedAnswers: Record<string, number>;
	mcqs: MCQ[];
	resultCardCloseHandler: () => void;
};

type SubjectScore = {
	subject: string;
	score: string;
};

type ScoreSummary = {
	scoresBySubject: SubjectScore[];
	totalCorrect: number;
	totalQuestions: number;
};

export default function MCQResult({
	open,
	mcqs,
	selectedAnswers,
	resultCardCloseHandler,
}: MCQResultProps) {
	function calculateScoresBySubject(
		questions: MCQ[],
		submittedAnswers: Record<string, number>
	): ScoreSummary {
		const subjects: Record<string, MCQ[]> = {};
		let totalCorrect = 0;
		const totalQuestions = questions.length;

		// Group questions by subject
		for (const question of questions) {
			const { subject } = question;
			if (!subjects[subject]) {
				subjects[subject] = [];
			}
			subjects[subject].push(question);
		}

		const scoresBySubject: SubjectScore[] = [];

		for (const [subject, subjectQuestions] of Object.entries(subjects)) {
			let correct = 0;

			for (const question of subjectQuestions) {
				const userAnswer = submittedAnswers[question.id];
				if (userAnswer !== undefined && userAnswer === question.correctAnswer) {
					correct++;
				}
			}

			totalCorrect += correct;

			scoresBySubject.push({
				subject,
				score: `${correct} / ${subjectQuestions.length}`,
			});
		}

		return {
			scoresBySubject,
			totalCorrect,
			totalQuestions,
		};
	}
	const {
		scoresBySubject: scores,
		totalCorrect,
		totalQuestions,
	} = calculateScoresBySubject(mcqs, selectedAnswers);

	const handleOnClick = () => {
		resultCardCloseHandler();
	};

	return (
		<Dialog open={open} onOpenChange={resultCardCloseHandler}>
			{/* <DialogTrigger className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition">
        Open Dialog
      </DialogTrigger> */}

			<DialogContent className="bg-gradient-to-br from-indigo-500 to-purple-500 text-white rounded-xl max-w-3xl shadow-2xl p-6 mb-5">
				<DialogHeader className="text-center">
					<DialogTitle className="mb-5 uppercase tracking-wider text-sm text-white text-center">
						YOUR{" "}
						<span className="bg-yellow-300 text-black px-2 rounded font-semibold">
							RESULTS
						</span>
					</DialogTitle>

					<div className="text-4xl font-bold flex justify-center items-center gap-2">
						🎉
					</div>

					<div className="mt-5 text-4xl font-bold flex justify-center items-center gap-2">
						<span>You scored</span>{" "}
						<span className="text-white">
							{totalCorrect}/{totalQuestions}
						</span>{" "}
						<span className="text-md font-normal">MCQ's!</span>
					</div>

					<DialogDescription className="text-white text-center text-sm leading-relaxed">
						Keep working. Keep learning. Stay locked in. <br />
						<span className="text-green-300 font-semibold">
							Good Job Champ!
						</span>
					</DialogDescription>
				</DialogHeader>

				<div className="mt-6">
					<h4 className="text-left text-white mb-2 text-sm">
						Subject wise Summary
					</h4>
					<div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
						{scores?.reverse()?.map((item, index) => (
							<div
								key={index}
								className="bg-white text-black text-sm flex justify-between items-center px-3 py-2 rounded-md shadow"
							>
								<span>⚡ {item.subject}</span>
								<span>{item.score}</span>
							</div>
						))}
					</div>
				</div>

				<DialogFooter className="mt-6 flex justify-center">
					<button
						onClick={handleOnClick}
						className="px-6 mx-auto py-2 bg-white text-black rounded-md hover:bg-gray-200 transition"
					>
						Done
					</button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
