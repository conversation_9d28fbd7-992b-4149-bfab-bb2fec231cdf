import { createFileRoute } from "@tanstack/react-router";
import { useState, useRef, useEffect } from "react";
import { useMediaQuery } from "react-responsive";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Send, X } from "react-feather";
import YouTube, { YouTubeProps } from "react-youtube";
import { extractYouTubeVideoId } from "@/lib/utils";
import { useGrokKeyStatus, useMCQChatbot } from "@/lib/queries/chatbot.query";
import { useGetResourceById } from "@/lib/queries/resources.query";
import GrokKeyBanner from "@/components/chatbot/grok-key-banner";
import { useToast } from "@/hooks/use-toast";
import MobileHeader from "@/components/common/mobile-header";

// Message types for the chat interface
interface ChatMessage {
	id: string;
	content: string;
	sender: "user" | "ai";
	timestamp: Date;
	isLoading?: boolean;
}

const VideoResourcePage = () => {
	const { resourceId } = Route.useParams();
	const { toast } = useToast();
	const isDesktop = useMediaQuery({ minWidth: 1024 });

	// Chatbot states
	const [showChatbot, setShowChatbot] = useState(false);
	const [currentMessage, setCurrentMessage] = useState("");
	const [messages, setMessages] = useState<ChatMessage[]>([]);
	const chatContainerRef = useRef<HTMLDivElement>(null);

	// Queries
	const {
		data: resource,
		isLoading: isResourceLoading,
		error: resourceError,
	} = useGetResourceById(resourceId);
	const { data: grokKeyStatus } = useGrokKeyStatus();
	const chatbotMutation = useMCQChatbot();

	// Auto-scroll to bottom when new messages are added
	const scrollToBottom = () => {
		if (chatContainerRef.current) {
			chatContainerRef.current.scrollTop =
				chatContainerRef.current.scrollHeight;
		}
	};

	useEffect(() => {
		if (messages[messages.length - 1]?.isLoading) {
			scrollToBottom();
		}
	}, [messages]);

	// Handle loading and error states
	if (isResourceLoading) {
		return (
			<div className="min-h-screen bg-gray-50 flex items-center justify-center">
				<div className="text-center">
					<div className="w-8 h-8 border-2 border-purple-600 border-t-transparent rounded-full animate-spin mx-auto mb-4" />
					<p className="text-gray-600">Loading resource...</p>
				</div>
			</div>
		);
	}

	if (resourceError || !resource) {
		return (
			<div className="min-h-screen bg-gray-50 flex items-center justify-center">
				<div className="text-center">
					<p className="text-red-600 mb-4">Failed to load resource</p>
					<p className="text-gray-600">
						Resource not found or an error occurred
					</p>
				</div>
			</div>
		);
	}

	// YouTube player options
	const youtubeOpts: YouTubeProps["opts"] = {
		width: "100%",
		height: "100%",
		playerVars: {
			autoplay: 0,
		},
	};

	// Handle chatbot submission with message-based system
	const handleChatbotSubmit = async () => {
		if (!currentMessage.trim() || !grokKeyStatus?.has_groq_api_key || !resource)
			return;

		const userMessage: ChatMessage = {
			id: Date.now().toString(),
			content: currentMessage,
			sender: "user",
			timestamp: new Date(),
		};

		// Add user message and loading AI message
		const loadingMessage: ChatMessage = {
			id: (Date.now() + 1).toString(),
			content: "",
			sender: "ai",
			timestamp: new Date(),
			isLoading: true,
		};

		setMessages((prev) => [...prev, userMessage, loadingMessage]);
		setCurrentMessage("");

		// Create dummy MCQ-based payload for video resource
		const payload = {
			mcqid: resource._id,
			mcqTitle: resource.title,
			options: ["A. Option 1", "B. Option 2", "C. Option 3", "D. Option 4"],
			userChoice: "A. Option 1",
			correctAnswer: "B. Option 2",
			explanation: resource.description,
			question: currentMessage,
		};

		try {
			const response = await chatbotMutation.mutateAsync(payload);
			const content = response.data.result?.choices?.[0]?.message?.content;

			if (content) {
				// Replace loading message with actual response
				setMessages((prev) =>
					prev.map((msg) =>
						msg.id === loadingMessage.id
							? { ...msg, content, isLoading: false }
							: msg
					)
				);
			} else {
				throw new Error("Invalid response format");
			}
		} catch (error: any) {
			// Remove loading message on error
			setMessages((prev) => prev.filter((msg) => msg.id !== loadingMessage.id));

			toast({
				title: "Error",
				description:
					error?.response?.data?.message || "Failed to get chatbot response",
				variant: "destructive",
			});
		}
	};

	if (isDesktop) {
		return (
			<div className="min-h-screen bg-gray-50">
				<div className="container mx-auto px-4 py-6">
					<div className="grid grid-cols-1 lg:grid-cols-3 2xl:grid-cols-4 gap-6">
						{/* Main Content - Video and Tabs */}
						<div className="lg:col-span-2 2xl:col-span-3">
							{/* Video Player */}
							<div className="bg-white rounded-lg shadow-sm overflow-hidden mb-6">
								<div className="aspect-video">
									<YouTube
										videoId={extractYouTubeVideoId(resource.url)}
										opts={youtubeOpts}
										className="w-full h-full"
									/>
								</div>
							</div>

							{/* Overview Button */}
							<Button className="w-[154px] h-[46px] flex-none rounded-[9.6px] font-semibold text-sm leading-[140%] tracking-[-0.01em] text-center bg-[#4338CA] text-white border-[0.688944px] border-[#EFF0F6] shadow-[inset_0px_-2.06683px_4.13367px_rgba(244,245,250,0.6)] mb-4">
								Overview
							</Button>
							{/* About Video Content */}
							<div className="bg-white rounded-lg p-4 shadow-sm">
								<h2 className="text-xl font-semibold mb-4">About this video</h2>
								<p className="text-gray-600 whitespace-pre-line">
									{resource.description}
								</p>
							</div>
						</div>

						{/* Sidebar - Chatbot */}
						<div className="lg:col-span-1">
							<div className="bg-white rounded-lg shadow-sm p-4 sticky top-6 max-h-[calc(100vh-2rem)] overflow-y-auto">
								{!showChatbot ? (
									/* Ask Chatbot Button */
									<div className="mb-4">
										<Button
											variant="outline"
											onClick={() => setShowChatbot(true)}
											className="flex items-center gap-2 text-purple-600 border-purple-200 hover:bg-purple-50 w-full justify-center"
										>
											<span className="text-lg">🤖</span>
											Ask Chatbot
										</Button>
									</div>
								) : (
									/* Full Chatbot Interface */
									<div className="flex flex-col justify-between space-y-4 h-[500px]">
										{/* Header with close button */}
										<div>
											<div className="flex items-center justify-between mb-4">
												<div className="flex items-center gap-2">
													<div className="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center">
														<span className="text-white text-sm">🤖</span>
													</div>
													<h3 className="font-semibold">AI Chatbot</h3>
												</div>
												<Button
													variant="icon"
													size="sm"
													onClick={() => setShowChatbot(false)}
												>
													<X size={16} />
												</Button>
											</div>
											{/* Grok Key Banner */}
											{messages.length === 0 && (
												<GrokKeyBanner
													hasGrokKey={grokKeyStatus?.has_groq_api_key || false}
													className="mb-4"
												/>
											)}
										</div>

										{/* Chat Messages or Start Chat */}
										<div
											className="overflow-y-auto mb-4"
											ref={chatContainerRef}
										>
											{messages.length === 0 ? (
												<div className="text-center">
													<h4 className="font-semibold text-lg mb-2">
														Start a Chat!
													</h4>
													<p className="text-sm text-gray-600">
														Ask AI questions about the specific question or
														topic.
													</p>
												</div>
											) : (
												<div className="space-y-3">
													{messages.map((message) => (
														<div
															key={message.id}
															className={`flex ${
																message.sender === "user"
																	? "justify-end"
																	: "justify-start"
															}`}
														>
															<div
																className={`max-w-[80%] rounded-lg p-3 ${
																	message.sender === "user"
																		? "rounded-br-none bg-purple-600 text-white"
																		: "rounded-bl-none bg-gray-100 text-gray-800"
																}`}
															>
																{message.isLoading ? (
																	<div className="flex items-center gap-2">
																		<div className="w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin" />
																		<span className="text-sm">Thinking...</span>
																	</div>
																) : (
																	<p className="text-sm whitespace-pre-wrap">
																		{message.content}
																	</p>
																)}
																<p className="text-xs opacity-70 mt-1">
																	{message.timestamp.toLocaleTimeString([], {
																		hour: "2-digit",
																		minute: "2-digit",
																	})}
																</p>
															</div>
														</div>
													))}
												</div>
											)}
										</div>

										{/* Chat Input */}
										<div className="flex gap-2">
											<Input
												placeholder="Write a message!"
												value={currentMessage}
												onChange={(e) => setCurrentMessage(e.target.value)}
												disabled={!grokKeyStatus?.has_groq_api_key}
												onKeyDown={(e) => {
													if (
														e.key === "Enter" &&
														grokKeyStatus?.has_groq_api_key
													) {
														handleChatbotSubmit();
													}
												}}
											/>
											<Button
												onClick={handleChatbotSubmit}
												disabled={
													chatbotMutation.isPending ||
													!currentMessage.trim() ||
													!grokKeyStatus?.has_groq_api_key
												}
												className="bg-purple-600 hover:bg-purple-700"
											>
												{chatbotMutation.isPending ? (
													<div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
												) : (
													<Send size={16} />
												)}
											</Button>
										</div>
									</div>
								)}
							</div>
						</div>
					</div>
				</div>
			</div>
		);
	}

	// Mobile Layout
	return (
		<div className="min-h-screen bg-white">
			<MobileHeader resourceType="video" />
			<div className="pt-[80px]">
				{/* Video Player */}
				<div className="bg-white aspect-video">
					<YouTube
						videoId={extractYouTubeVideoId(resource.url)}
						opts={youtubeOpts}
						className="w-full h-full"
					/>
				</div>

				{/* Title and Subtitle */}
				<div className="p-4 border-b flex flex-col items-start gap-2 mb-4">
					<h1 className="text-base font-semibold self-stretch">
						{resource.title}
					</h1>
					{/* <p className="text-sm font-normal text-gray-600 self-stretch">Subtitle</p> */}
				</div>

				{/* Tabs */}
				<Tabs defaultValue="chatbot" className="w-full">
					<TabsList className="flex flex-row items-center justify-center gap-4 p-4">
						<TabsTrigger
							value="chatbot"
							className="px-6 py-2 font-semibold text-sm rounded-[9.6px] text-black data-[state=active]:text-white data-[state=active]:bg-[#4338CA] data-[state=active]:border-[0.688944px] data-[state=active]:border-[#EFF0F6] data-[state=active]:shadow-[inset_0px_-2.06683px_4.13367px_rgba(244,245,250,0.6)]"
						>
							AI Chatbot
						</TabsTrigger>
						<TabsTrigger
							value="overview"
							className="px-6 py-2 font-semibold text-sm rounded-[9.6px] text-black data-[state=active]:text-white data-[state=active]:bg-[#4338CA] data-[state=active]:border-[0.688944px] data-[state=active]:border-[#EFF0F6] data-[state=active]:shadow-[inset_0px_-2.06683px_4.13367px_rgba(244,245,250,0.6)]"
						>
							Overview
						</TabsTrigger>
					</TabsList>

					<TabsContent value="overview" className="px-4 pb-4">
						<div className="space-y-4">
							<h2 className="text-lg font-semibold">About this video</h2>
							<p className="text-gray-600 whitespace-pre-line">
								{resource.description}
							</p>
						</div>
					</TabsContent>

					<TabsContent
						value="chatbot"
						className="p-4 h-[calc(100vh-300px)] flex flex-col"
					>
						{/* Grok Key Banner */}
						{messages.length === 0 && (
							<GrokKeyBanner
								hasGrokKey={grokKeyStatus?.has_groq_api_key || false}
								className="mb-4"
							/>
						)}

						{/* Chat Messages or Start Chat */}
						<div className="overflow-y-auto mb-4" ref={chatContainerRef}>
							{messages.length === 0 ? (
								<div className="text-center">
									<h3 className="text-lg font-semibold mb-2">Start a Chat!</h3>
									<p className="text-sm text-gray-600">
										Ask AI questions about the specific question or topic.
									</p>
								</div>
							) : (
								<div className="space-y-3">
									{messages.map((message) => (
										<div
											key={message.id}
											className={`flex ${
												message.sender === "user"
													? "justify-end"
													: "justify-start"
											}`}
										>
											<div
												className={`max-w-[80%] rounded-lg p-3 ${
													message.sender === "user"
														? "bg-purple-600 text-white"
														: "bg-gray-100 text-gray-800"
												}`}
											>
												{message.isLoading ? (
													<div className="flex items-center gap-2">
														<div className="w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin" />
														<span className="text-sm">Thinking...</span>
													</div>
												) : (
													<p className="text-sm whitespace-pre-wrap">
														{message.content}
													</p>
												)}
												<p className="text-xs opacity-70 mt-1">
													{message.timestamp.toLocaleTimeString([], {
														hour: "2-digit",
														minute: "2-digit",
													})}
												</p>
											</div>
										</div>
									))}
								</div>
							)}
						</div>

						{/* Chat Input */}
						<div className="flex gap-2">
							<Input
								placeholder="Write a message!"
								value={currentMessage}
								onChange={(e) => setCurrentMessage(e.target.value)}
								disabled={!grokKeyStatus?.has_groq_api_key}
								onKeyDown={(e) => {
									if (e.key === "Enter" && grokKeyStatus?.has_groq_api_key) {
										handleChatbotSubmit();
									}
								}}
							/>
							<Button
								onClick={handleChatbotSubmit}
								disabled={
									chatbotMutation.isPending ||
									!currentMessage.trim() ||
									!grokKeyStatus?.has_groq_api_key
								}
								className="bg-purple-600 hover:bg-purple-700"
							>
								{chatbotMutation.isPending ? (
									<div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
								) : (
									<Send size={16} />
								)}
							</Button>
						</div>
					</TabsContent>
				</Tabs>
			</div>
		</div>
	);
};

export const Route = createFileRoute("/(app)/_mainlayout/resource/$resourceId")(
	{
		component: VideoResourcePage,
	}
);
