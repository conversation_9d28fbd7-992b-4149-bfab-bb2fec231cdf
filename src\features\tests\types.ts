import { APIResponse } from "@/lib/api/types";

export interface ChosenOption {
  mcqId: string;
  chosenOption: number | null;
}

export interface SubmitQuizPayload {
  quizId: string;
  timeTaken: number;
  chosenOptions: ChosenOption[];
}

export interface SubmitQuizResponse {
  status: number;
  success: boolean;
  message: string;
}

// Quiz types for single quiz API
export interface QuizMCQ {
	_id: string;
	subject: string;
	type: string;
	title: string;
	topic: string;
	difficulty: "easy" | "medium" | "hard";
	repitition: boolean;
	resource?: string;
	explanation?: string;
	entryTest: string[];
	options: string[];
	answer?: number; // Optional because it might be excluded for incomplete quizzes
	chapter: number;
	chosenOption?: number | null; // User's chosen option
}

export interface QuizResponse {
	_id: string;
	type: string;
	entryTest: string;
	createdAt: string;
	completed: boolean;
	totalTime: number;
	timeTaken: number;
	subtype: string;
	testName: string;
	bookmarked: boolean;
	mcqs: QuizMCQ[];
	user_id: string;
}

export type getQuizResponse = APIResponse<QuizResponse>;