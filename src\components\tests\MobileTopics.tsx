import * as React from "react";
import { createPortal } from "react-dom";
import { ChevronLeft, X } from "lucide-react";

import TopicsSearchBar from "@/components/tests/TopicSearchBar";
import TopicItems from "@/components/tests/TopicItems";

import type { SelectedTopic, TopicFormData } from "@/features/tests/custom";

type ComboMaps = Record<string, Record<string, number>>;

function useLockBodyScroll(locked: boolean) {
	React.useEffect(() => {
		if (!locked) return;
		const scrollY = window.scrollY || window.pageYOffset;
		const original = {
			top: document.body.style.top,
			position: document.body.style.position,
			overflow: document.body.style.overflow,
			width: document.body.style.width,
		};
		document.body.style.top = `-${scrollY}px`;
		document.body.style.position = "fixed";
		document.body.style.overflow = "hidden";
		document.body.style.width = "100%";
		return () => {
			const y = Math.abs(parseInt(document.body.style.top || "0", 10)) || 0;
			document.body.style.top = original.top;
			document.body.style.position = original.position;
			document.body.style.overflow = original.overflow;
			document.body.style.width = original.width;
			window.scrollTo(0, y);
		};
	}, [locked]);
}

function useMounted() {
	const [mounted, setMounted] = React.useState(false);
	React.useEffect(() => setMounted(true), []);
	return mounted;
}

type MobileTopicsProps = {
	open: boolean;
	onClose: () => void;

	mode: "custom" | "subject";

	subjectId: string;
	subjectName: string;
	topics: string[];

	subjectNumQuestions?: string;
	onChangeSubjectNumQuestions?: (subjectId: string, value: string) => void;

	selectedTopics: SelectedTopic[];
	setSelectedTopics: React.Dispatch<React.SetStateAction<SelectedTopic[]>>;

	comboMaps: ComboMaps;
	setComboMaps: React.Dispatch<React.SetStateAction<ComboMaps>>;

	topicFormData: Record<string, TopicFormData>;
	setTopicFormData: React.Dispatch<
		React.SetStateAction<Record<string, TopicFormData>>
	>;

	totalSelectedMCQs: number;

	isPairAlreadySelected: (
		subjectId: string,
		topic: string,
		type: string,
		difficulty: string
	) => boolean;
	getSelectedPairsSet: (subjectId: string, topic: string) => Set<string>;
	getAllTypesForTopic: (subjectId: string, topic: string) => string[];
	getAllDiffsForTopic: (subjectId: string, topic: string) => string[];
	getTypesForTopicHavingDifficulty: (
		subjectId: string,
		topic: string,
		difficulty: string
	) => string[];
	getDiffsForTopicType: (
		subjectId: string,
		topic: string,
		type: string
	) => string[];
	getMaxForPair: (
		subjectId: string,
		topic: string,
		type: string,
		difficulty: string
	) => number;

	mcqCap?: number;
};

const MobileTopics: React.FC<MobileTopicsProps> = (props) => {
	const mounted = useMounted();
	useLockBodyScroll(props.open);

	// per-topic inputs handled inside TopicItems; no subject-level count here

	// search state
	const [query, setQuery] = React.useState("");

	const filtered = React.useMemo(() => {
		const q = query.trim().toLowerCase();
		if (!q) return props.topics ?? [];
		return (props.topics ?? []).filter((t) => t.toLowerCase().includes(q));
	}, [props.topics, query]);

	const selectedForThis = React.useMemo(
		() => props.selectedTopics.filter((t) => t.subjectId === props.subjectId),
		[props.selectedTopics, props.subjectId]
	);

	const removeSelectedTopic = (topicId: string) => {
		const topicToRemove = props.selectedTopics.find((t) => t.id === topicId);
		if (!topicToRemove) return;

		props.setSelectedTopics((prev) => prev.filter((t) => t.id !== topicId));
		props.setComboMaps((prev) => {
			const next = { ...prev };
			const cm = { ...(next[topicToRemove.subjectId] || {}) };
			const k = `${topicToRemove.name.toLowerCase()}__${topicToRemove.testType.toLowerCase()}__${topicToRemove.difficulty.toLowerCase()}`;
			cm[k] = (cm[k] ?? 0) + (parseInt(topicToRemove.numQuestions, 10) || 0);
			next[topicToRemove.subjectId] = cm;
			return next;
		});
	};

	if (!mounted) return null;
	// type labels handled in TopicItems rendering

	return createPortal(
		<>
			<div
				className={[
					"fixed inset-0 bg-black/50 z-[2147483646]",
					"transition-opacity duration-300 ease-in-out",
					props.open ? "opacity-100" : "opacity-0 pointer-events-none",
				].join(" ")}
				onClick={props.onClose}
			/>

			<div
				role="dialog"
				aria-modal="true"
				aria-hidden={!props.open}
				className={[
					"fixed inset-0 z-[2147483647] bg-white flex flex-col h-full",
					"transition-transform duration-300 ease-in-out",
					"will-change-transform",
					"motion-reduce:transition-none motion-reduce:transform-none",
					props.open ? "translate-x-0" : "translate-x-full",
				].join(" ")}
			>
				{/* Header */}
				<div className="z-10 bg-white border-b border-gray-200">
					<div className="max-w-[1160px] mx-auto px-4 py-3 flex items-center justify-between relative">
						<button
							onClick={props.onClose}
							className="flex items-center gap-1 text-gray-700"
						>
							<ChevronLeft className="w-5 h-5" />
							<span className="text-sm">Back</span>
						</button>

						<h2 className="absolute left-1/2 -translate-x-1/2 text-base font-semibold text-gray-900">
							Add Topics
						</h2>

						<div className="text-right space-y-1" />
					</div>
				</div>

				{/* Body */}
				<div className="flex-1 overflow-y-auto overscroll-contain min-h-0 flex flex-col">
					<div className="max-w-[1160px] mx-auto px-4 py-4 flex flex-col flex-1">
						<div className="space-y-4 flex-shrink-0">
							<p className="text-sm text-gray-600">
								Search and add as many chapters as you want to attempt the test
								from.
							</p>

							{selectedForThis.length > 0 && (
								<div className="space-y-2">
									<h5 className="text-gray-700 font-medium">Selected Topics</h5>

									<div className="flex flex-col gap-2">
										{selectedForThis.map((topic) => (
											<div
												key={topic.id}
												className="w-full rounded-lg border border-gray-200 bg-white p-3"
											>
												<div className="flex items-start justify-between gap-3">
													<div className="flex-1 min-w-0">
														<h3
															className="text-gray-900 font-medium leading-5 text-sm"
															style={{
																wordBreak: "break-word",
																overflowWrap: "break-word",
																hyphens: "auto",
															}}
														>
															{topic.name}
														</h3>
													</div>

													<button
														onClick={() => removeSelectedTopic(topic.id)}
														className="p-1 rounded-full hover:bg-gray-100 text-gray-500 flex-shrink-0 mt-0"
														aria-label={`Remove ${topic.name}`}
													>
														<X className="w-4 h-4" />
													</button>
												</div>

												<div
													className="mt-2 -mx-1 overflow-x-auto"
													style={{ WebkitOverflowScrolling: "touch" }}
												>
													<div className="inline-flex items-center gap-2 px-1 whitespace-nowrap text-xs text-gray-700">
														<div className="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center shrink-0">
															<svg
																className="w-3 h-3 text-white"
																fill="none"
																stroke="currentColor"
																viewBox="0 0 24 24"
															>
																<path
																	strokeLinecap="round"
																	strokeLinejoin="round"
																	strokeWidth={2}
																	d="M5 13l4 4L19 7"
																/>
															</svg>
														</div>

														<span className="text-sm text-green-600 font-medium">
															Added
														</span>
														<span className="h-4 w-px bg-gray-300" />

														<span className="inline-flex items-center rounded-full bg-gray-100 px-2 py-1">
															{topic.numQuestions}{" "}
															{Number(topic.numQuestions) === 1
																? "question"
																: "questions"}
														</span>

														{props.mode === "custom" && (
															<>
																<span className="inline-flex items-center rounded-full bg-gray-100 px-2 py-1">
																	{topic.testType?.toLowerCase() === "roha"
																		? "cramming"
																		: topic.testType}
																</span>
																<span className="inline-flex items-center rounded-full bg-gray-100 px-2 py-1">
																	{topic.difficulty}
																</span>
															</>
														)}
													</div>
												</div>
											</div>
										))}
									</div>
								</div>
							)}

							<div className="flex items-center justify-between">
								<h6 className="text-gray-900 font-medium">Chapters</h6>
							</div>

							<TopicsSearchBar
								mode={props.mode}
								value={query}
								onChange={setQuery}
								placeholder={`Search in ${props.subjectName}`}
							/>

							<div className="pt-1 text-xs text-gray-500">
								{query.trim() ? "Search Results" : "Available Topics"}
							</div>
						</div>

						{/* Topics list */}
						<div className="flex-1 min-h-0 mt-4">
							<TopicItems
								mode={props.mode}
								subjectId={props.subjectId}
								topics={filtered}
								searchTerm={query}
								selectedTopics={props.selectedTopics}
								setSelectedTopics={props.setSelectedTopics}
								comboMaps={props.comboMaps}
								setComboMaps={props.setComboMaps}
								topicFormData={props.topicFormData}
								setTopicFormData={props.setTopicFormData}
								isPairAlreadySelected={props.isPairAlreadySelected}
								getSelectedPairsSet={props.getSelectedPairsSet}
								getAllTypesForTopic={props.getAllTypesForTopic}
								getAllDiffsForTopic={props.getAllDiffsForTopic}
								getTypesForTopicHavingDifficulty={
									props.getTypesForTopicHavingDifficulty
								}
								getDiffsForTopicType={props.getDiffsForTopicType}
								getMaxForPair={props.getMaxForPair}
								totalSelectedMCQs={props.totalSelectedMCQs}
								className="h-full"
							/>
						</div>
					</div>
				</div>
			</div>
		</>,
		document.body
	);
};

export default MobileTopics;
