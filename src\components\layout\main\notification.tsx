"use client";

import { useState, useRef, useEffect } from "react";
import {
	Tooltip,
	TooltipContent,
	TooltipTrigger,
	TooltipProvider,
} from "@/components/ui/tooltip";
import { Button } from "@/components/ui/button";
import { Bell } from "react-feather";
import clsx from "clsx";
import NotificationsDropdown from "@/components/common/notifications-dropdown";

const Notifications = ({ disabled = false }: { disabled?: boolean }) => {
	const [showDropdown, setShowDropdown] = useState(false);
	const notificationRef = useRef<HTMLDivElement>(null);

	const handleNotificationClick = () => {
		if (!disabled) {
			setShowDropdown(!showDropdown);
		}
	};

	const handleCloseDropdown = () => {
		setShowDropdown(false);
	};

	// Handle click outside to close dropdown
	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (
				notificationRef.current &&
				!notificationRef.current.contains(event.target as Node)
			) {
				setShowDropdown(false);
			}
		};

		document.addEventListener("mousedown", handleClickOutside);
		return () => {
			document.removeEventListener("mousedown", handleClickOutside);
		};
	}, []);

	return (
		<div className="relative" ref={notificationRef}>
			<TooltipProvider disableHoverableContent>
				<Tooltip delayDuration={100}>
					<TooltipTrigger asChild>
						<Button
							variant="ghost"
							size="icon"
							disabled={disabled}
							onClick={handleNotificationClick}
							className={clsx("border rounded-full p-3", {
								"border-gray-200 text-gray-600 hover:cursor-pointer hover:bg-gray-50":
									!disabled,
								"border-gray-100 text-gray-400 cursor-not-allowed bg-gray-50":
									disabled,
							})}
						>
							<Bell size={24} />
						</Button>
					</TooltipTrigger>
					{!disabled && (
						<TooltipContent side="bottom">Notifications</TooltipContent>
					)}
				</Tooltip>
			</TooltipProvider>

			{showDropdown && !disabled && (
				<NotificationsDropdown onClose={handleCloseDropdown} />
			)}
		</div>
	);
};

export default Notifications;
