import { api } from "@/lib/api";
import {
	addBookmarkReq,
	DataMap,
	getBookmarksReq,
	getBookmarksResp,
} from "./types";

export const getBookmarksList = <T extends keyof DataMap>(
	params: getBookmarksReq<T>
) => {
	const { category, page, limit } = params;
	return api.get<getBookmarksResp<T>>(
		`bookmark/get?category=${category}&page=${page}&limit=${limit}`
	);
};

export const addBookmark = (data: addBookmarkReq) => {
	return api.patch(`bookmark/add`, data);
};

export const deleteBookmark = (data: addBookmarkReq) => {
	return api.delete(`bookmark/remove`, { data });
};
