import * as React from "react";
import { Search } from "lucide-react";
import { cn } from "@/lib/utils";

type SubjectQuestionsProps = {
	value: string | number;
	onChange: (value: string) => void;
	min?: number;
	disabled?: boolean;
	placeholder?: string;
};

type TopicsSearchBarProps = {
	value: string;
	onChange: (value: string) => void;
	placeholder: string;
	mode?: "custom" | "subject";
	questionsNode?: React.ReactNode;
	questionsProps?: SubjectQuestionsProps;
	className?: string;
	inputClassName?: string;
	autoFocus?: boolean;
};

const TopicsSearchBar: React.FC<TopicsSearchBarProps> = ({
	value,
	onChange,
	placeholder,
	mode = "custom",
	questionsNode,
	questionsProps,
	className = "",
	inputClassName,
	autoFocus = false,
}) => {
	const isSubject = mode === "subject";
	const showSubjectQuestions = isSubject && (!!questionsNode || !!questionsProps);

	return (
		<div
			className={cn(
				isSubject
					? "flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3"
					: "flex items-center gap-3",
				className
			)}
		>
			{/* Search input */}
			<div
				className={cn(
					"relative h-[45px] sm:h-[55px]",
					isSubject
						? "w-full sm:flex-1 sm:min-w-[260px]"
						: "w-full sm:w-[456px]"
				)}
			>
				<Search
					className={cn(
						"pointer-events-none absolute top-1/2 -translate-y-1/2 text-gray-400",
						"left-3 w-4 h-4",
						"sm:left-4 sm:w-5 sm:h-5"
					)}
				/>
				<input
					type="text"
					value={value}
					onChange={(e) => onChange(e.target.value)}
					placeholder={placeholder}
					autoFocus={autoFocus}
					className={cn(
						`
              w-full h-full
              border border-gray-300 rounded-xl
              pl-10 pr-3 py-2 text-sm
              sm:pl-12 sm:pr-[14px] sm:py-[27px]
              focus:outline-none focus:ring-2 focus:ring-blue-500
            `,
						inputClassName
					)}
					style={{ fontFamily: "Inter, sans-serif", fontWeight: 400 }}
				/>
			</div>

			{/* SUBJECT mode questions (optional) */}
			{showSubjectQuestions && (
				<>
					<div className="sm:hidden w-full">
						{questionsNode ? (
							questionsNode
						) : (
							<input
								type="number"
								min={questionsProps?.min ?? 1}
								disabled={questionsProps?.disabled}
								value={String(questionsProps?.value ?? "")}
								onChange={(e) => questionsProps?.onChange?.(e.target.value)}
								placeholder={questionsProps?.placeholder ?? "No. of Questions"}
								className={cn(
									"w-full h-[45px] px-2 py-1 mt-1",
									"text-sm text-center outline-none",
									"border border-gray-300 rounded-md placeholder-gray-700"
								)}
								style={{ fontFamily: "Inter, sans-serif", fontWeight: 400 }}
								aria-label="No. of Questions"
							/>
						)}
					</div>

					<div className="ml-auto hidden sm:block">
						{questionsNode ? (
							questionsNode
						) : (
							<input
								type="number"
								min={questionsProps?.min ?? 1}
								disabled={questionsProps?.disabled}
								value={String(questionsProps?.value ?? "")}
								onChange={(e) => questionsProps?.onChange?.(e.target.value)}
								placeholder={questionsProps?.placeholder ?? "No. of Questions"}
								className={cn(
									"w-40 h-[55px] px-2 py-1",
									"text-sm text-center outline-none",
									"border border-gray-300 rounded-md placeholder-gray-700"
								)}
								style={{ fontFamily: "Inter, sans-serif", fontWeight: 400 }}
								aria-label="No. of Questions"
							/>
						)}
					</div>
				</>
			)}
		</div>
	);
};

export default TopicsSearchBar;
