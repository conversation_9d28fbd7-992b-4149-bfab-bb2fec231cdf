import { useState } from "react";
import {
	<PERSON><PERSON>,
	<PERSON>alog<PERSON>ontent,
	<PERSON><PERSON><PERSON>eader,
	<PERSON>alogTitle,
	DialogDescription,
	DialogFooter,
} from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { AlertTriangle } from "react-feather";
import { toast } from "@/hooks/use-toast";
import { reportMCQ } from "@/features/mcqs/services";
import { ReportReason } from "@/features/mcqs/types";

type ReportMCQDialogProps = {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	mcqId: string;
};

const reportReasons: {
	value: ReportReason;
	label: string;
	description: string;
}[] = [
	{
		value: "incorrect_answer",
		label: "Incorrect Answer",
		description: "The marked correct answer is wrong",
	},
	{
		value: "unclear_question",
		label: "Unclear Question",
		description: "The question is confusing or ambiguous",
	},
	{
		value: "typo_error",
		label: "Typo/Grammar Error",
		description: "There are spelling or grammar mistakes",
	},
	{
		value: "inappropriate_content",
		label: "Inappropriate Content",
		description: "Contains offensive or inappropriate material",
	},
	{
		value: "duplicate_question",
		label: "Duplicate Question",
		description: "This question appears multiple times",
	},
	{
		value: "other",
		label: "Other",
		description: "A different issue not listed above",
	},
];

export default function ReportMCQDialog({
	open,
	onOpenChange,
	mcqId,
}: ReportMCQDialogProps) {
	const [selectedReasons, setSelectedReasons] = useState<ReportReason[]>([]);
	const [comment, setComment] = useState("");
	const [isSubmitting, setIsSubmitting] = useState(false);

	const handleReasonToggle = (reason: ReportReason, checked: boolean) => {
		if (checked) {
			setSelectedReasons((prev) => [...prev, reason]);
		} else {
			setSelectedReasons((prev) => prev.filter((r) => r !== reason));
		}
	};

	const handleSubmit = async () => {
		if (selectedReasons.length === 0) {
			toast({
				title: "Error",
				description: "Please select at least one reason for reporting.",
				variant: "destructive",
			});
			return;
		}

		if (comment.length > 500) {
			toast({
				title: "Error",
				description: "Comment cannot be longer than 500 characters.",
				variant: "destructive",
			});
			return;
		}

		try {
			setIsSubmitting(true);

			await reportMCQ({
				id: mcqId,
				reasons: selectedReasons,
				comment: comment.trim(),
			});

			toast({
				title: "Report Submitted",
				description: "Thank you for your feedback. We'll review this MCQ.",
			});

			// Reset form and close dialog
			setSelectedReasons([]);
			setComment("");
			onOpenChange(false);
		} catch (error) {
			console.error("Failed to report MCQ:", error);
			toast({
				title: "Error",
				description: "Failed to submit report. Please try again.",
				variant: "destructive",
			});
		} finally {
			setIsSubmitting(false);
		}
	};

	const handleCancel = () => {
		setSelectedReasons([]);
		setComment("");
		onOpenChange(false);
	};

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			{/* 🔥 Added scrolling support */}
			<DialogContent className="max-w-sm mx-4 max-h-[80vh] overflow-y-auto">
				<DialogHeader>
					<DialogTitle className="flex items-center gap-2">
						<AlertTriangle className="w-5 h-5 text-red-500" />
						Report MCQ
					</DialogTitle>
					<DialogDescription>
						Help us improve by reporting issues with this question.
					</DialogDescription>
				</DialogHeader>

				<div className="space-y-4">
					{/* Reason Selection */}
					<div>
						<Label className="text-sm font-medium text-gray-700 mb-3 block">
							What's wrong with this question? (Select all that apply) *
						</Label>
						<div className="space-y-3">
							{reportReasons.map((reason) => (
								<div key={reason.value} className="flex items-start space-x-3">
									<Checkbox
										id={reason.value}
										checked={selectedReasons.includes(reason.value)}
										onCheckedChange={(checked) =>
											handleReasonToggle(reason.value, checked as boolean)
										}
										className="mt-1"
									/>
									<div className="flex-1">
										<Label
											htmlFor={reason.value}
											className="text-sm font-medium text-gray-900 cursor-pointer"
										>
											{reason.label}
										</Label>
										<p className="text-xs text-gray-500 mt-0.5">
											{reason.description}
										</p>
									</div>
								</div>
							))}
						</div>
					</div>

					{/* Comment Section */}
					<div>
						<Label className="text-sm font-medium text-gray-700 mb-2 block">
							Additional Comments (Optional)
						</Label>
						<textarea
							value={comment}
							onChange={(e) => setComment(e.target.value)}
							placeholder="Describe the issue in detail (optional)..."
							className="w-full h-20 px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
							maxLength={500}
						/>
						<div className="flex justify-end items-center mt-1">
							<p className="text-xs text-gray-500">{comment.length}/500</p>
						</div>
					</div>
				</div>

				<DialogFooter className="flex gap-2">
					<Button
						variant="outline"
						onClick={handleCancel}
						disabled={isSubmitting}
					>
						Cancel
					</Button>
					<Button
						onClick={handleSubmit}
						disabled={isSubmitting}
						className="bg-red-500 hover:bg-red-600 text-white"
					>
						{isSubmitting ? "Submitting..." : "Submit Report"}
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
