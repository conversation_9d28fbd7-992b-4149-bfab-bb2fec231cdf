import React, { useState } from "react";
import { createFile<PERSON><PERSON><PERSON>, useNavigate } from "@tanstack/react-router";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useAddGrokKey } from "@/lib/queries/chatbot.query";
import { useToast } from "@/hooks/use-toast";
import { ArrowLeft, Eye, EyeOff, ExternalLink } from "react-feather";
import { ICONS } from "@/lib/assets/images";

const AddGrokKeyPage = () => {
	const [step, setStep] = useState(1);
	const [grokKey, setGrokKey] = useState("");
	const [showKey, setShowKey] = useState(false);
	const navigate = useNavigate();
	const { toast } = useToast();

	const addGrokKeyMutation = useAddGrokKey();

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();

		const key = grokKey.trim();

		// Basic frontend validation: must start with 'gsk_' and be 56 characters long
		if (!key) {
			toast({
				title: "Error",
				description: "Please enter a valid Grok API key",
				variant: "destructive",
			});
			return;
		}

		if (!key.startsWith("gsk_") || key.length !== 56) {
			toast({
				title: "Error",
				description:
					"Groq API key must start with 'gsk_' and be 56 characters long",
				variant: "destructive",
			});
			return;
		}

		try {
			await addGrokKeyMutation.mutateAsync({ groq_api_key: grokKey.trim() });
			toast({
				title: "Success",
				description: "Groq API key added successfully!",
			});
			navigate({ to: "/dashboard" });
		} catch (error: any) {
			toast({
				title: "Error",
				description:
					error?.response?.data?.message || "Failed to add Grok API key",
				variant: "destructive",
			});
		}
	};

	const handleReturn = () => {
		window.history.back();
	};

	// Step 1: Informational screen
	const renderStep1 = () => (
		<div className="w-full max-w-2xl mx-auto bg-white rounded-lg shadow-xl p-6 sm:p-8 flex flex-col items-center gap-6">
			<div className="w-full flex flex-col justify-center items-center gap-3 text-center">
				<h2 className="font-bold text-2xl sm:text-3xl md:text-4xl leading-tight tracking-tight text-black">
					Add Groq API Key
				</h2>
				<p className="font-medium text-base md:text-lg leading-relaxed text-gray-600 max-w-lg">
					Add your own Groq API key for a faster, private chatbot experience.
				</p>
				<ul className="text-gray-600 text-sm md:text-base list-disc list-inside text-left max-w-lg space-y-1">
					<li>Faster responses during busy times</li>
					<li>Your conversations stay private to your account</li>
					<li>No usage limits — use it as much as you need</li>
				</ul>
				<p className="font-medium text-base md:text-lg leading-relaxed text-[#5936CD] font-bold max-w-lg">
					And it's free!
				</p>
			</div>
			<div className="w-full flex flex-col sm:flex-row-reverse gap-4 max-w-md mx-auto">
				<Button
					onClick={() => setStep(2)}
					className="w-full rounded-lg bg-[#5936CD] text-white"
				>
					Add your own Groq key
				</Button>
				<Button
					variant="outline"
					onClick={handleReturn}
					className="w-full rounded-lg hidden sm:flex"
				>
					Return
				</Button>
			</div>
		</div>
	);

	// Step 2: Input screen
	const renderStep2 = () => (
		<div className="w-full max-w-2xl mx-auto bg-white rounded-lg shadow-xl p-6 sm:p-8 flex flex-col items-center gap-6">
			<div className="w-full flex flex-col justify-center items-center gap-3 text-center">
				<h2 className="font-bold text-2xl sm:text-3xl md:text-4xl leading-tight tracking-tight text-black">
					Add Groq API Key
				</h2>
				<h3 className="font-bold text-xl md:text-2xl leading-relaxed text-[#5936CD]">
					Instructions
				</h3>
				<ol className="font-medium text-sm md:text-base leading-relaxed text-gray-600 max-w-lg list-decimal list-inside text-left space-y-2">
					<li>
						Open{" "}
						<a
							href="https://console.groq.com/keys"
							target="_blank"
							rel="noopener noreferrer"
							aria-label="Open Groq console in a new tab"
							className="inline-flex items-center gap-2 text-[#5936CD] hover:underline"
						>
							console.groq.com/keys
							<ExternalLink size={14} className="text-[#5936CD]" />
						</a>
					</li>
					<li>
						<span className="text-[#5936CD] font-semibold">Sign up</span> or log
						in using Google or your email.
					</li>
					<li>
						Click the{" "}
						<span className="text-[#5936CD] font-semibold">
							"Create API Key"
						</span>{" "}
						button.
					</li>
					<li>
						Name it anything, like{" "}
						<span className="text-[#5936CD] font-semibold">"Parhlai"</span>.
					</li>
					<li>
						<span className="text-[#5936CD] font-semibold">Copy</span> the
						generated API key.
					</li>
					<li>
						<span className="text-[#5936CD] font-semibold">
							Return to Parhlai
						</span>
						, paste the key in the field below, and click "Add Key".
					</li>
				</ol>
			</div>
			<form
				onSubmit={handleSubmit}
				className="w-full flex flex-col items-center gap-4 max-w-lg"
			>
				<div className="relative w-full">
					<Input
						type={showKey ? "text" : "password"}
						placeholder="gsk_xxxxxxxx"
						value={grokKey}
						onChange={(e) => setGrokKey(e.target.value)}
						className="h-12 pr-12 text-base"
						disabled={addGrokKeyMutation.isPending}
					/>
					<button
						type="button"
						onClick={() => setShowKey(!showKey)}
						className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
					>
						{showKey ? <EyeOff size={20} /> : <Eye size={20} />}
					</button>
				</div>
				<Button
					type="submit"
					className="w-full rounded-lg bg-[#5936CD] text-white"
					disabled={addGrokKeyMutation.isPending}
				>
					{addGrokKeyMutation.isPending ? "Adding..." : "Add Key"}
				</Button>
				<Button
					type="button"
					variant="link"
					onClick={() => setStep(1)}
					className="text-gray-500"
				>
					Back
				</Button>
			</form>
		</div>
	);

	return (
		<div className="relative w-full min-h-screen bg-white flex flex-col justify-center items-center p-4 sm:p-6">
			{/* Background Ellipses */}
			<div className="absolute w-[477px] h-[477px] left-0 top-[574px] bg-[rgba(89,54,205,0.7)] blur-[250px]"></div>
			<div className="absolute w-[477px] h-[477px] left-[1092px] top-[-109px] bg-[rgba(89,54,205,0.7)] blur-[250px]"></div>

			{/* Logo on desktop, Back button on mobile */}
			<div className="absolute left-4 top-4 sm:left-20 sm:top-16">
				<button
					onClick={handleReturn}
					className="flex items-center gap-2 text-gray-600 hover:text-black sm:hidden"
				>
					<ArrowLeft size={20} />
				</button>
				<img
					src={ICONS.logoexpanded}
					alt="Logo"
					className="hidden sm:block w-40 h-auto"
				/>
			</div>

			{/* Centered Content */}
			<div className="relative z-10 w-full">
				{step === 1 ? renderStep1() : renderStep2()}
			</div>
		</div>
	);
};

export const Route = createFileRoute("/(app)/_chatbot/add-key")({
	component: AddGrokKeyPage,
});
