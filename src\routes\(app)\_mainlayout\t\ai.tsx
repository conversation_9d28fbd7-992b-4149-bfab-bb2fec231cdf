import { useMediaQuery } from "react-responsive";
import { Navbar } from "@/components/layout/main/navbar";
import { createFileRoute, useNavigate } from "@tanstack/react-router";
import { useEffect, useMemo, useState } from "react";
import { useToast } from "@/hooks/use-toast";
import {
	getMockTests,
	MockTest,
	getSubjectTestParameters,
} from "@/features/tests/custom.api";
import { Subject } from "@/features/tests/custom";
import { ChevronLeft } from "lucide-react";
import BottomActionsBar from "@/components/tests/BottomActionsBar";
import TestSelectionModal from "@/components/tests/TestSelectionModel";
import PrepBanner from "@/components/common/PrepBanner";
import { api } from "@/lib/api";
import { APIResponse } from "@/lib/api/types";

function AITestPage() {
	const { toast } = useToast();
	const navigate = useNavigate();

	const [showTestModal, setShowTestModal] = useState(false);
	const [tests, setTests] = useState<MockTest[]>([]);
	const [loadingTests, setLoadingTests] = useState(false);
	const [testsError, setTestsError] = useState<string | null>(null);
	const [selectedPrep, setSelectedPrep] = useState("NUST-Engineering");
	// AI test specific state
	const [subjects, setSubjects] = useState<Subject[]>([]);
	const [subjectMcqCounts, setSubjectMcqCounts] = useState<
		Record<string, number>
	>({});
	const [loadingParams, setLoadingParams] = useState(false);
	const [paramsError, setParamsError] = useState<string | null>(null);
	const [generating, setGenerating] = useState(false);

	// Load available tests
	useEffect(() => {
		let cancelled = false;
		(async () => {
			try {
				setLoadingTests(true);
				setTestsError(null);
				const res = await getMockTests();
				const list = (res.data?.data?.mockTests ?? []) as MockTest[];
				if (cancelled) return;

				setTests(list);
				const first = list[0];
				if (first && !list.some((t) => t.testId === selectedPrep)) {
					setSelectedPrep(first.testId);
				}
			} catch (err: any) {
				if (cancelled) return;
				setTestsError(
					err?.response?.data?.message ||
						err?.message ||
						"Failed to fetch tests."
				);
			} finally {
				if (!cancelled) setLoadingTests(false);
			}
		})();
		return () => {
			cancelled = true;
		};
	}, [selectedPrep]);

	// Load subjects for AI test
	useEffect(() => {
		let cancelled = false;

		async function load() {
			if (!selectedPrep) return;

			try {
				setLoadingParams(true);
				setParamsError(null);
				// reset UI for fresh load
				setSubjectMcqCounts({});
				setSubjects([]);

				const res = await getSubjectTestParameters(selectedPrep);
				const data = res.data?.data as {
					subjects: Record<
						string,
						{
							totalMcqs: number;
							topics: { topic: string; totalMcqs: number }[];
						}
					>;
				};

				const backendSubjects = data?.subjects ?? {};
				const newSubjects: Subject[] = [];

				Object.entries(backendSubjects).forEach(
					([subjectName, subjectData]) => {
						const id = subjectName.toLowerCase().replace(/[^a-z0-9]+/g, "-");
						newSubjects.push({
							id,
							name: subjectName,
							count: subjectData.totalMcqs ?? 0,
							topics: [],
							availableCount: subjectData.totalMcqs,
						});
					}
				);

				if (cancelled) return;
				setSubjects(newSubjects);
			} catch (e: any) {
				if (!cancelled) {
					setParamsError(
						e?.response?.data?.message || "Failed to load subject parameters."
					);
					setSubjects([]);
				}
			} finally {
				if (!cancelled) setLoadingParams(false);
			}
		}

		load();
		return () => {
			cancelled = true;
		};
	}, [selectedPrep]);

	const selectedTestName = useMemo(() => {
		const test = tests.find((t) => t.testId === selectedPrep);
		return test?.name ?? "Select Test";
	}, [tests, selectedPrep]);

	const generateAITest = async () => {
		setGenerating(true);

		try {
			// Validate that at least one subject has MCQs
			const totalMcqs = Object.values(subjectMcqCounts).reduce(
				(a, b) => a + (b || 0),
				0
			);
			if (totalMcqs === 0) {
				toast({
					title: "No MCQs Selected",
					description: "Please select at least one MCQ for any subject.",
					variant: "destructive",
				});
				return;
			}

			// Make API call to generate AI test
			const response = await api.post<APIResponse<any>>("/quiz/ai/generate", {
				entryTest: selectedPrep,
				subjectMcqCounts: subjectMcqCounts,
			});

			if (response.data && response.data.success) {
				// Store test data in localStorage to avoid URL length limitations
				localStorage.setItem(
					"currentTestData",
					JSON.stringify(response.data.data)
				);

				// Navigate to MCQs page
				navigate({ to: "/mcqs" });

				toast({
					title: "Success",
					description: "AI test generated successfully!",
				});
			} else {
				throw new Error(response.data?.message || "Failed to generate AI test");
			}
		} catch (err: any) {
			const msg =
				err?.response?.data?.message ||
				err?.message ||
				"Failed to generate AI test.";
			toast({
				title: "Generation failed",
				description: msg,
				variant: "destructive",
			});
		} finally {
			setGenerating(false);
		}
	};

	const resetTest = () => {
		setSubjectMcqCounts({});
		setSubjects((prev) => prev.map((s) => ({ ...s, count: 0 })));
		toast({ title: "Reset", description: "Data reset successfully!" });
	};

	const totalSelectedMCQs = useMemo(() => {
		return Object.values(subjectMcqCounts).reduce((a, b) => a + (b || 0), 0);
	}, [subjectMcqCounts]);

	const totalSelectedSubjects = useMemo(() => {
		return Object.values(subjectMcqCounts).filter((v) => (v || 0) > 0).length;
	}, [subjectMcqCounts]);
	const mcqCap = 200;
	const capped = totalSelectedMCQs >= mcqCap;

	const handlePrepChange = (prepTestId: string) => {
		setSelectedPrep(prepTestId);
		setShowTestModal(false);
	};

	const isDesktop = useMediaQuery({ minWidth: 1024 });

	return (
		<div className='min-h-screen bg-gray-50 font-["Inter",sans-serif]'>
			{!isDesktop && <Navbar />}
			<div className="max-w-[1160px] mx-auto p-6">
				{/* Header */}
				<div className="mb-8">
					<div className="sm:hidden flex items-center justify-between mb-4">
						<button
							onClick={() => window.history.back()}
							aria-label="Go back"
							className="p-2 -ml-2 rounded-md hover:bg-gray-100 active:bg-gray-200"
						>
							<ChevronLeft className="w-5 h-5 text-gray-700" />
						</button>

						<h2
							className="text-base font-semibold text-gray-900"
							style={{ fontFamily: "Inter, sans-serif" }}
						>
							AI Test
						</h2>

						<div className="w-9" />
					</div>

					<PrepBanner
						className="mb-10"
						value={selectedTestName}
						onChangeClick={() => setShowTestModal(true)}
						maxPillWidth={360}
					/>

					<div className="flex flex-col items-start gap-2 sm:gap-[15px] w-full">
						<h1 className="font-inter font-bold text-2xl sm:text-[32px] leading-[30px] sm:leading-[39px] text-[#211C37]">
							AI Test
						</h1>

						{/* mobile meta  */}
						<div className="w-full sm:hidden flex items-start justify-between gap-3">
							<p className="font-inter font-normal text-sm leading-[18px] text-[#64748B] pr-4">
								Our AI will create a focused, personalized test for your prep.
							</p>

							<div className="text-right space-y-1 shrink-0">
								<div className="text-xs text-gray-500">
									{totalSelectedSubjects} subject(s)
								</div>
								<div className="text-sm text-gray-700">
									<div className="text-xs text-gray-500">Total MCQs</div>
									<div
										className={`font-medium ${capped ? "text-red-600" : ""}`}
									>
										{totalSelectedMCQs} / {mcqCap}
									</div>
								</div>
							</div>
						</div>

						{/* desktop meta */}
						<div className="w-full hidden sm:flex items-start justify-between gap-3">
							<p className="font-inter font-normal text-base leading-[24px] text-[#64748B] max-w-[630px]">
								Our AI will create a focused, personalized test for your prep.
							</p>
						</div>
					</div>
				</div>

				{/* Loading/Error  */}
				{loadingParams && (
					<div className="mb-6 text-sm text-gray-600">Loading subjects…</div>
				)}
				{paramsError && (
					<div className="mb-6 text-sm text-red-600">{paramsError}</div>
				)}

				{/* Subject Cards (AI): per-subject MCQ inputs only */}
				<div className="space-y-4">
					{subjects.map((subject) => (
						<SubjectCountCard
							key={subject.id}
							subject={subject}
							value={subjectMcqCounts[subject.name] ?? 0}
							onChange={(newVal) => {
								setSubjectMcqCounts((prev) => ({
									...prev,
									[subject.name]: newVal,
								}));
							}}
							totalSelected={totalSelectedMCQs}
							cap={mcqCap}
						/>
					))}
				</div>

				<div className="mt-8 flex justify-center">
					<BottomActionsBar
						mcqs={{ value: totalSelectedMCQs, cap: mcqCap }}
						count={{ value: totalSelectedSubjects, label: "subjects" }}
						onReset={resetTest}
						onAttempt={generateAITest}
						loading={loadingParams || generating}
						attemptDisabled={
							totalSelectedMCQs === 0 || loadingParams || generating
						}
					/>
				</div>

				{/* Test Selection Modal */}
				{showTestModal && (
					<TestSelectionModal
						open={showTestModal}
						tests={tests}
						selectedPrep={selectedPrep}
						onClose={() => setShowTestModal(false)}
						onSelect={handlePrepChange}
						loading={loadingTests}
						error={testsError}
					/>
				)}
			</div>
		</div>
	);
}

export const Route = createFileRoute("/(app)/_mainlayout/t/ai")({
	component: AITestPage,
});

// --- Internal: Subject count card for AI page ---
import * as React from "react";
import {
	BookOpen,
	Atom,
	Calculator,
	FlaskConical,
	Microscope,
	Cpu,
	PenTool,
	Landmark,
	Globe,
	LineChart,
	Puzzle,
	ClipboardList,
} from "lucide-react";

const slug = (s: string) =>
	s
		.toLowerCase()
		.replace(/[^a-z0-9]+/g, "-")
		.replace(/(^-|-$)/g, "");

const ICON_MAP: Record<string, React.ComponentType<any>> = {
	physics: Atom,
	mathematics: Calculator,
	chemistry: FlaskConical,
	biology: Microscope,
	english: BookOpen,
	"computer-science": Cpu,
	drawing: PenTool,
	"drawing-&-design": PenTool,
	history: Landmark,
	geography: Globe,
	economics: LineChart,
	analytical: Puzzle,
	"subject-test": ClipboardList,
};

type SubjectCountCardProps = {
	subject: Subject;
	value: number;
	onChange: (val: number) => void;
	totalSelected: number;
	cap: number;
};

const SubjectCountCard: React.FC<SubjectCountCardProps> = ({
	subject,
	value,
	onChange,
	totalSelected,
	cap,
}) => {
	const subjectSlug = slug(subject.name);
	const Icon =
		ICON_MAP[subjectSlug] ||
		ICON_MAP[subjectSlug.replace(/-and-/g, "-&-")] ||
		BookOpen;
	const available = subject.availableCount ?? subject.count ?? 0;

	const remainingGlobal = Math.max(0, cap - (totalSelected - (value || 0)));
	const maxForThis = Math.min(available, remainingGlobal);

	const handleChange = (raw: string) => {
		if (raw === "") {
			onChange(0);
			return;
		}
		let n = parseInt(raw, 10) || 0;
		if (n < 0) n = 0;
		if (n > maxForThis) n = maxForThis;
		onChange(n);
	};

	return (
		<div className="bg-white rounded-2xl border border-gray-200 shadow-sm">
			<div className="px-5 py-4 flex items-center justify-between gap-3">
				<div className="flex items-center gap-3 sm:gap-4 flex-1 min-w-0">
					<div className="w-11 h-11 sm:w-12 sm:h-12 rounded-xl flex items-center justify-center bg-[#EAE5F7]">
						<Icon className="w-6 h-6 sm:w-7 sm:h-7 text-[#5936CD]" />
					</div>
					<div className="min-w-0">
						<h3
							className="text-lg text-gray-900"
							style={{ fontFamily: "Inter, sans-serif", fontWeight: 400 }}
						>
							{subject.name}
						</h3>
					</div>
				</div>

				<div className="w-40">
					<div className="border rounded-md overflow-hidden w-full">
						<input
							type="number"
							min={0}
							max={maxForThis}
							value={value || 0}
							onChange={(e) => handleChange(e.target.value)}
							placeholder="No. Of Questions"
							className="w-full px-2 py-2 text-sm text-center outline-none border-0 placeholder-gray-500"
							style={{ fontFamily: "Inter, sans-serif", fontWeight: 400 }}
						/>
					</div>
					<div className="text-xs text-gray-500 mt-1 text-center">
						Max: {maxForThis}
					</div>
				</div>
			</div>
		</div>
	);
};
