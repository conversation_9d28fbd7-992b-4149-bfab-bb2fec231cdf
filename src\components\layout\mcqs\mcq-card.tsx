import { useState, useRef, useEffect } from "react";
import {
	Bookmark,
	AlertTriangle,
	ChevronDown,
	ChevronUp,
	XCircle,
	CheckCircle,
	Send,
} from "react-feather";
import { MCQ } from "@/features/mcqs/types";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { toggleMCQType } from "@/lib/utils";
import Latex from "react-latex-next";
import "katex/dist/katex.min.css";
import { Element } from "react-scroll";
import { addBookmark, deleteBookmark } from "@/features/bookmarks/services";
import { toast } from "@/hooks/use-toast";
import { useGrokKeyStatus, useMCQChatbot } from "@/lib/queries/chatbot.query";
import GrokKeyBanner from "@/components/chatbot/grok-key-banner";
import ReportMCQDialog from "./report-mcq-dialog";

// Message types for the chat interface
interface ChatMessage {
	id: string;
	content: string;
	sender: "user" | "ai";
	timestamp: Date;
	isLoading?: boolean;
}

// Wrapper component to fix TypeScript issues
const ScrollElement: React.FC<any> = Element as any;

type MCQCardProps = {
	mcq: MCQ;
	selectedAnswer: number | undefined;
	isCorrect?: boolean; // Changed to optional since we're not using it directly
	liveCheckEnabled: boolean;
	showResults: boolean;
	questionNumber?: number;
	bookmarkList: any;
	setRefetchBookmarks: any;
};

const MCQCard = ({
	mcq,
	selectedAnswer,
	// isCorrect removed from destructuring since it's not used
	liveCheckEnabled,
	showResults,
	questionNumber,
	bookmarkList,
	setRefetchBookmarks,
}: MCQCardProps) => {
	const [showExpertAnswer, setShowExpertAnswer] = useState(false);
	const [isReportDialogOpen, setIsReportDialogOpen] = useState(false);
	const [showChatbot, setShowChatbot] = useState(false);
	const [chatbotQuestion, setChatbotQuestion] = useState("");
	const [chatbotMessages, setChatbotMessages] = useState<ChatMessage[]>([]);
	const chatContainerRef = useRef<HTMLDivElement>(null);

	const hasAnswered = selectedAnswer !== undefined;
	const showFeedback = (liveCheckEnabled && hasAnswered) || showResults;
	const isBookmarked = bookmarkList?.mcq?.mcqs?.filter(
		(item: any) => item?._id === mcq.id
	).length
		? true
		: false;

	// Grok key status and chatbot mutation
	const { data: grokKeyStatus } = useGrokKeyStatus();
	const chatbotMutation = useMCQChatbot();

	// Auto-scroll to bottom when new messages are added
	const scrollToBottom = () => {
		if (chatContainerRef.current) {
			chatContainerRef.current.scrollTop =
				chatContainerRef.current.scrollHeight;
		}
	};

	useEffect(() => {
		if (chatbotMessages[chatbotMessages.length - 1]?.isLoading) {
			scrollToBottom();
		}
	}, [chatbotMessages]);

	console.log("Bookmark", isBookmarked, bookmarkList);

	const removeBookmark = async () => {
		const response = await deleteBookmark({ category: "mcq", id: mcq.id });
		console.log("removeBookmark", response);
		if (response?.data?.status === 200 && response?.data?.success) {
			toast({
				title: "Success",
				description: "Bookmark removed!",
			});
		} else {
			toast({
				title: "Error",
				description: "Failed to remove the bookmark",
				variant: "destructive",
			});
		}
	};

	const saveBookmark = async () => {
		const response = await addBookmark({ category: "mcq", id: mcq.id });
		console.log("toggleBookmark", response);
		if (response?.data?.status === 200 && response?.data?.success) {
			toast({
				title: "Success",
				description: "MCQ bookmarked!",
			});
		} else {
			toast({
				title: "Error",
				description: "Failed to bookmark the MCQ",
				variant: "destructive",
			});
		}
	};

	const toggleBookmark = async () => {
		if (isBookmarked) {
			await removeBookmark();
		} else {
			await saveBookmark();
		}
		setRefetchBookmarks((prev: any) => !prev);
	};

	const handleChatbotSubmit = async () => {
		if (!chatbotQuestion.trim()) {
			toast({
				title: "Error",
				description: "Please enter a question",
				variant: "destructive",
			});
			return;
		}

		const userMessage: ChatMessage = {
			id: Date.now().toString(),
			content: chatbotQuestion,
			sender: "user",
			timestamp: new Date(),
		};

		// Add user message and loading AI message
		const loadingMessage: ChatMessage = {
			id: (Date.now() + 1).toString(),
			content: "",
			sender: "ai",
			timestamp: new Date(),
			isLoading: true,
		};

		setChatbotMessages((prev) => [...prev, userMessage, loadingMessage]);
		setChatbotQuestion(""); // Clear the input immediately

		const userChoice =
			selectedAnswer !== undefined
				? `${String.fromCharCode(65 + selectedAnswer)}.${mcq.options[selectedAnswer]}`
				: "No answer selected";

		const correctAnswer =
			mcq.correctAnswer !== undefined
				? `${String.fromCharCode(65 + mcq.correctAnswer)}.${mcq.options[mcq.correctAnswer]}`
				: "Answer not available";

		const payload = {
			mcqid: mcq.id,
			mcqTitle: mcq.question,
			options: mcq.options.map(
				(option, index) => `${String.fromCharCode(65 + index)}.${option}`
			),
			userChoice,
			correctAnswer,
			explanation: mcq.description || "",
			question: userMessage.content,
		};

		try {
			const response = await chatbotMutation.mutateAsync(payload);
			const content = response.data.result?.choices?.[0]?.message?.content;
			if (content) {
				// Replace loading message with actual response
				setChatbotMessages((prev) =>
					prev.map((msg) =>
						msg.id === loadingMessage.id
							? { ...msg, content, isLoading: false }
							: msg
					)
				);
			} else {
				throw new Error("Invalid response format");
			}
		} catch (error: any) {
			// Remove loading message on error
			setChatbotMessages((prev) =>
				prev.filter((msg) => msg.id !== loadingMessage.id)
			);

			toast({
				title: "Error",
				description:
					error?.response?.data?.message || "Failed to get chatbot response",
				variant: "destructive",
			});
		}
	};

	// Determine if the given answer is actually correct (regardless of isCorrect prop)
	const isActuallyCorrect =
		mcq.correctAnswer !== undefined && selectedAnswer === mcq.correctAnswer;

	const difficultyStyles = {
		easy: "border-green-300 bg-green-100 text-green-800",
		medium: "border-yellow-300 bg-yellow-100 text-yellow-800",
		hard: "border-red-300 bg-red-100 text-red-800",
	};

	return (
		<ScrollElement name={mcq.id} className="p-6">
			<div className="flex items-center justify-between mb-4">
				<div className="flex items-center gap-2">
					<p className="text-gray-500 font-bold">
						QUESTION #{questionNumber !== undefined ? questionNumber : mcq.id}
					</p>
					{mcq.tag && (
						<span className="text-sm px-2 py-0.5 font-semibold border border-purple-300 bg-purple-100 text-accent rounded-full">
							{toggleMCQType(mcq.tag)}
						</span>
					)}
					{mcq.topic && (
						<span className="text-sm px-2 py-0.5 font-semibold border border-blue-300 bg-blue-100 text-blue-700 rounded-full">
							{mcq.topic}
						</span>
					)}
					{mcq.difficulty && (
						<span
							className={`text-sm px-2 py-0.5 font-semibold rounded-full border ${difficultyStyles[mcq.difficulty] || ""}`}
						>
							{mcq.difficulty}
						</span>
					)}
				</div>
				<div className="flex items-center gap-4 mx-4">
					<Button
						variant="icon"
						className="[&_svg]:size-5 px-0"
						onClick={toggleBookmark}
					>
						<Bookmark
							className={`w-5 h-5 text-gray-600 ${isBookmarked ? "fill-accent" : ""}`}
						/>
					</Button>
					<Button
						variant="icon"
						className="[&_svg]:size-5 px-0"
						onClick={() => setIsReportDialogOpen(true)}
						title="Report this MCQ"
					>
						<AlertTriangle className="w-5 h-5 text-gray-600 hover:text-red-500" />
					</Button>
				</div>
			</div>

			<h3 className="mb-8 text-xl">
				<Latex>{mcq.question}</Latex>
			</h3>

			<div className="space-y-2">
				<p className="font-bold text-gray-400 mb-6">CHOOSE ANSWER</p>
				{mcq.options.map((option, index) => (
					<div
						key={mcq.id + option + index}
						className={`p-1 ${showFeedback && mcq.correctAnswer !== undefined && index === mcq.correctAnswer ? "bg-green-50 rounded" : ""}`}
					>
						<span
							className={`text-gray-400 ${showFeedback && mcq.correctAnswer !== undefined && index === mcq.correctAnswer ? "font-bold" : ""}`}
						>
							({String.fromCharCode(65 + index)})
						</span>
						<span
							className={`text-base font-semibold ml-2 ${
								showFeedback &&
								mcq.correctAnswer !== undefined &&
								index === mcq.correctAnswer
									? "text-green-600"
									: showFeedback &&
										  index === selectedAnswer &&
										  !isActuallyCorrect
										? "text-red-600"
										: ""
							}`}
						>
							<Latex>{option}</Latex>
						</span>
						{showFeedback &&
							mcq.correctAnswer !== undefined &&
							index === mcq.correctAnswer && (
								<span className="ml-2 text-xs bg-green-100 text-green-700 px-2 py-0.5 rounded">
									Correct Answer
								</span>
							)}
					</div>
				))}
			</div>

			{showFeedback && (
				<div className="mt-4">
					<div className="flex flex-col item-start sm:flex-row sm:items-center sm:justify-between gap-4">
						<div
							className={`py-2 px-4 rounded-full max-w-[90%] ${
								isActuallyCorrect
									? "bg-green-100 text-green-700"
									: "bg-red-100 text-red-700"
							} flex items-center gap-2`}
						>
							<span className="text-sm flex items-center gap-2">
								{isActuallyCorrect ? (
									<CheckCircle className="w-4 h-4" />
								) : (
									<XCircle className="w-4 h-4" />
								)}
								{isActuallyCorrect
									? "You have chosen the correct answer."
									: hasAnswered
										? "You have chosen the wrong answer."
										: "You have not answered this question."}
							</span>
						</div>
						<Button
							variant="link"
							onClick={() => setShowExpertAnswer(!showExpertAnswer)}
							className="justify-start text-gray-600 flex items-center gap-1 text-sm"
						>
							{showExpertAnswer ? "Hide" : "View"} expert answer
							{showExpertAnswer ? (
								<ChevronUp size={16} />
							) : (
								<ChevronDown size={16} />
							)}
						</Button>
					</div>

					{showExpertAnswer && (
						<div className="mt-2 p-4 bg-gray-50 rounded-md">
							<p className="text-sm">
								<Latex>{String(mcq.description)}</Latex>
							</p>
							{!isActuallyCorrect && mcq.correctAnswer !== undefined && (
								<div className="mt-2 pt-2 border-t border-gray-200">
									<p className="text-sm font-medium text-green-600">
										Correct answer:{" "}
										{String.fromCharCode(65 + mcq.correctAnswer)} -{" "}
										<Latex>
											{String(mcq.options[mcq.correctAnswer] ?? "")}
										</Latex>
									</p>
								</div>
							)}
						</div>
					)}

					{/* Ask Chatbot Section - Only for wrong answers and when expert answer is shown */}
					{showExpertAnswer && !isActuallyCorrect && (
						<div className="mt-4">
							{/* Ask Chatbot Button */}
							<div className="mb-4">
								<Button
									variant="outline"
									onClick={() => setShowChatbot(!showChatbot)}
									className="flex items-center gap-2 text-purple-600 border-purple-200 hover:bg-purple-50"
								>
									<span className="text-lg">🤖</span>
									Ask Chatbot
								</Button>
							</div>

							{/* Chatbot Input Section */}
							{showChatbot && (
								<div className="space-y-4">
									{/* Grok Key Banner */}
									{chatbotMessages.length === 0 && (
										<GrokKeyBanner
											hasGrokKey={grokKeyStatus?.has_groq_api_key || false}
											className="mb-4"
										/>
									)}

									{/* Chat Messages */}
									<div className="rounded-md">
										{chatbotMessages.length === 0 ? (
											<div className="text-center py-4">
												<h4 className="font-semibold text-sm mb-2">
													Start a Chat!
												</h4>
												<p className="text-xs text-gray-600">
													Ask AI questions about this specific MCQ.
												</p>
											</div>
										) : (
											<div
												className="max-h-60 overflow-y-auto mb-4 space-y-3 p-3 bg-gray-50 rounded-lg"
												onWheel={(e) => e.stopPropagation()}
												onTouchMove={(e) => e.stopPropagation()}
												ref={chatContainerRef}
											>
												{chatbotMessages.map((message) => (
													<div
														key={message.id}
														className={`flex ${
															message.sender === "user"
																? "justify-end"
																: "justify-start"
														}`}
													>
														<div
															className={`max-w-[80%] rounded-lg p-3 ${
																message.sender === "user"
																	? "rounded-br-none bg-purple-600 text-white"
																	: "rounded-bl-none bg-white text-gray-800 border"
															}`}
														>
															{message.isLoading ? (
																<div className="flex items-center gap-2">
																	<div className="w-3 h-3 border-2 border-gray-400 border-t-transparent rounded-full animate-spin" />
																	<span className="text-xs">Thinking...</span>
																</div>
															) : (
																<p className="text-xs whitespace-pre-wrap">
																	{message.content}
																</p>
															)}
															<p className="text-xs opacity-70 mt-1">
																{message.timestamp.toLocaleTimeString([], {
																	hour: "2-digit",
																	minute: "2-digit",
																})}
															</p>
														</div>
													</div>
												))}
											</div>
										)}

										{/* Ask Chatbot input */}
										<div className="flex gap-2">
											<Input
												placeholder="Ask Chatbot..."
												value={chatbotQuestion}
												onChange={(e) => setChatbotQuestion(e.target.value)}
												disabled={!grokKeyStatus?.has_groq_api_key}
												className="flex-1"
												onKeyDown={(e) => {
													if (
														e.key === "Enter" &&
														grokKeyStatus?.has_groq_api_key
													) {
														handleChatbotSubmit();
													}
												}}
											/>
											<Button
												onClick={handleChatbotSubmit}
												disabled={
													chatbotMutation.isPending ||
													!chatbotQuestion.trim() ||
													!grokKeyStatus?.has_groq_api_key
												}
												className="bg-purple-600 hover:bg-purple-700"
											>
												{chatbotMutation.isPending ? (
													<div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
												) : (
													<Send size={16} />
												)}
											</Button>
										</div>
									</div>
								</div>
							)}
						</div>
					)}
				</div>
			)}

			<ReportMCQDialog
				open={isReportDialogOpen}
				onOpenChange={setIsReportDialogOpen}
				mcqId={mcq.id}
			/>
		</ScrollElement>
	);
};

export default MCQCard;
