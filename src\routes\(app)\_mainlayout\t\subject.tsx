import { useMediaQuery } from "react-responsive";
import { Navbar } from "@/components/layout/main/navbar";
import { createFileRoute, useNavigate } from "@tanstack/react-router";
import { useEffect, useMemo, useState } from "react";
import { useToast } from "@/hooks/use-toast";
import {
	getMockTests,
	MockTest,
	getSubjectTestParameters,
	generateSubjectTest,
} from "@/features/tests/custom.api";
import { Subject, SelectedTopic, TopicFormData } from "@/features/tests/custom";
import { ChevronLeft } from "lucide-react";
import BottomActionsBar from "@/components/tests/BottomActionsBar";
import SubjectCards from "@/components/tests/SubjectCards";
import TestSelectionModal from "@/components/tests/TestSelectionModel";
import PrepBanner from "@/components/common/PrepBanner";
import type { SubjectSelection } from "@/features/tests/custom.api";

const slug = (s: string) =>
	s
		.toLowerCase()
		.replace(/[^a-z0-9]+/g, "-")
		.replace(/(^-|-$)/g, "");

function buildSubjectSelections(
	selected: SelectedTopic[],
	subjects: Subject[]
): SubjectSelection[] {
	const idToName = new Map(subjects.map((s) => [s.id, s.name]));
	const bySubject: Record<string, Record<string, number>> = {};

	for (const t of selected) {
		const subjectName =
			idToName.get((t as any).subjectId) ??
			(t as any).subject ??
			(t as any).subjectName;

		const topic =
			(t as any).topic ?? (t as any).topicName ?? (t as any).name ?? undefined;

		const count = Number((t as any).numQuestions ?? 0);

		if (!subjectName || !topic || !Number.isFinite(count) || count <= 0) {
			continue;
		}

		if (!bySubject[subjectName]) bySubject[subjectName] = {};
		bySubject[subjectName][topic] =
			(bySubject[subjectName][topic] ?? 0) + count;
	}

	return Object.entries(bySubject).map(([subject, topicsMap]) => ({
		subject,
		topics: Object.entries(topicsMap).map(([topic, count]) => ({
			topic,
			count,
		})),
	}));
}
function SubjectTestPage() {
	const { toast } = useToast();
	const navigate = useNavigate();

	const [showTestModal, setShowTestModal] = useState(false);
	const [tests, setTests] = useState<MockTest[]>([]);
	const [loadingTests, setLoadingTests] = useState(false);
	const [testsError, setTestsError] = useState<string | null>(null);
	const [selectedPrep, setSelectedPrep] = useState("NUST-Engineering");
	const [attempting, setAttempting] = useState(false);

	const [subjects, setSubjects] = useState<Subject[]>([]);
	const [selectedTopics, setSelectedTopics] = useState<SelectedTopic[]>([]);
	const [topicFormData, setTopicFormData] = useState<
		Record<string, TopicFormData>
	>({});
	const [comboMaps, setComboMaps] = useState<
		Record<string, Record<string, number>>
	>({});

	// params loading/error
	const [loadingParams, setLoadingParams] = useState(false);
	const [paramsError, setParamsError] = useState<string | null>(null);

	// Load available tests
	useEffect(() => {
		let cancelled = false;
		(async () => {
			try {
				setLoadingTests(true);
				setTestsError(null);
				const res = await getMockTests();
				const list = (res.data?.data?.mockTests ?? []) as MockTest[];
				if (cancelled) return;

				setTests(list);
				const first = list[0];
				if (first && !list.some((t) => t.testId === selectedPrep)) {
					setSelectedPrep(first.testId);
				}
			} catch (e: any) {
				if (!cancelled) {
					setTestsError(e?.response?.data?.message || "Failed to load tests.");
					setTests([]);
				}
			} finally {
				if (!cancelled) setLoadingTests(false);
			}
		})();
		return () => {
			cancelled = true;
		};
	}, []);

	const selectedTestName = useMemo(
		() => tests.find((t) => t.testId === selectedPrep)?.name || selectedPrep,
		[tests, selectedPrep]
	);

	// Load Subject-Test parameters
	useEffect(() => {
		let cancelled = false;

		async function load() {
			try {
				setLoadingParams(true);
				setParamsError(null);

				// reset UI for fresh load
				setSelectedTopics([]);
				setTopicFormData({});
				setSubjects([]);
				setComboMaps({});

				const res = await getSubjectTestParameters(selectedPrep);
				const data = res.data?.data as {
					subjects: Record<
						string,
						{
							totalMcqs: number;
							topics: { topic: string; totalMcqs: number }[];
						}
					>;
				};

				const backendSubjects = data?.subjects ?? {};

				const newSubjects: Subject[] = [];
				const newComboMaps: Record<string, Record<string, number>> = {};

				Object.entries(backendSubjects).forEach(([subjectName, block]) => {
					const id = slug(subjectName);
					const topics = (block.topics ?? [])
						.map((t) => t.topic)
						.filter(Boolean);

					newSubjects.push({
						id,
						name: subjectName,
						count: block.totalMcqs ?? 0,
						topics: topics.sort(),
					});

					const cm: Record<string, number> = {};
					(block.topics ?? []).forEach((t) => {
						const key = `${t.topic.toLowerCase()}____`;
						cm[key] = t.totalMcqs ?? 0;
					});
					newComboMaps[id] = cm;
				});

				if (cancelled) return;
				setSubjects(newSubjects);
				setComboMaps(newComboMaps);
			} catch (e: any) {
				if (!cancelled) {
					setParamsError(
						e?.response?.data?.message || "Failed to load subjects."
					);
					setSubjects([]);
					setComboMaps({});
				}
			} finally {
				if (!cancelled) setLoadingParams(false);
			}
		}

		if (selectedPrep) load();
		return () => {
			cancelled = true;
		};
	}, [selectedPrep]);

	const totalSelectedMCQs = useMemo(
		() =>
			selectedTopics.reduce(
				(acc, t) => acc + (parseInt(t.numQuestions, 10) || 0),
				0
			),
		[selectedTopics]
	);
	const handleAttemptSubjectTest = async () => {
		if (attempting) return;

		try {
			setAttempting(true);

			if (!selectedPrep) {
				toast({
					title: "Select a test",
					description: "Please choose an entry test first.",
					variant: "destructive",
				});
				return;
			}
			if (selectedTopics.length === 0) {
				toast({
					title: "No topics selected",
					description:
						"Pick at least one topic with a positive question count.",
					variant: "destructive",
				});
				return;
			}

			const subjectSelections = buildSubjectSelections(
				selectedTopics,
				subjects
			);

			if (subjectSelections.length === 0) {
				toast({
					title: "Nothing to generate",
					description: "All selected counts appear to be 0.",
					variant: "destructive",
				});
				return;
			}

			const response = await generateSubjectTest({
				entryTest: selectedPrep,
				subjectSelections,
			});

			if (response.data && response.data.success) {
				localStorage.setItem(
					"currentTestData",
					JSON.stringify(response.data.data)
				);

				navigate({ to: "/mcqs" });

				toast({
					title: "Success",
					description: "Subject test generated successfully!",
				});
			} else {
				throw new Error("Invalid response format");
			}
		} catch (err: any) {
			const msg =
				err?.response?.data?.message ||
				err?.message ||
				"Failed to generate subject test.";
			toast({
				title: "Generation failed",
				description: msg,
				variant: "destructive",
			});
		} finally {
			setAttempting(false);
		}
	};

	const resetTest = () => {
		setSelectedTopics([]);
		setTopicFormData({});
		setSubjects((prev) => prev.map((s) => ({ ...s, count: 0 })));
		toast({ title: "Reset", description: "Data reset successfully!" });
	};

	const filteredSubjects = subjects;
	const totalSelectedTopics = selectedTopics.length;

	const mcqCap = 200;
	const capped = totalSelectedMCQs >= mcqCap;

	const isDesktop = useMediaQuery({ minWidth: 1024 });

	return (
		<div className='min-h-screen bg-gray-50 font-["Inter",sans-serif]'>
			{!isDesktop && <Navbar />}
			<div className="max-w-[1160px] mx-auto p-6">
				{/* Header */}
				<div className="mb-8">
					<div className="sm:hidden flex items-center justify-between mb-4">
						<button
							onClick={() => window.history.back()}
							aria-label="Go back"
							className="p-2 -ml-2 rounded-md hover:bg-gray-100 active:bg-gray-200"
						>
							<ChevronLeft className="w-5 h-5 text-gray-700" />
						</button>

						<h2
							className="text-base font-semibold text-gray-900"
							style={{ fontFamily: "Inter, sans-serif" }}
						>
							Subject Test
						</h2>

						<div className="w-9" />
					</div>

					<PrepBanner
						className="mb-10"
						value={selectedTestName}
						onChangeClick={() => setShowTestModal(true)}
						maxPillWidth={360}
					/>

					<div className="flex flex-col items-start gap-2 sm:gap-[15px] w-full">
						<h1 className="font-inter font-bold text-2xl sm:text-[32px] leading-[30px] sm:leading-[39px] text-[#211C37]">
							Subject Test
						</h1>

						{/* mobile meta  */}
						<div className="w-full sm:hidden flex items-start justify-between gap-3">
							<p className="font-inter font-normal text-sm leading-[18px] text-[#64748B] pr-4">
								Here is the list of subjects from your domain to choose from.
							</p>

							<div className="text-right space-y-1 shrink-0">
								<div className="text-xs text-gray-500">
									{totalSelectedTopics} topic(s)
								</div>
								<div className="text-sm text-gray-700">
									<div className="text-xs text-gray-500">Total MCQs</div>
									<div
										className={`font-medium ${capped ? "text-red-600" : ""}`}
									>
										{totalSelectedMCQs} / {mcqCap}
									</div>
								</div>
							</div>
						</div>

						<p className="font-inter font-normal text-sm sm:text-base leading-[18px] sm:leading-[19px] text-[#64748B] pr-4 hidden sm:block">
							Choose subjects and topics below to build a customized subject
							test; adjust question counts as needed.
						</p>
					</div>
				</div>

				{/* Loading/Error  */}
				{loadingParams && (
					<div className="mb-6 text-sm text-gray-600">Loading subjects…</div>
				)}
				{paramsError && (
					<div className="mb-6 text-sm text-red-600">{paramsError}</div>
				)}

				{/* Subject Cards  */}
				<SubjectCards
					mode="subject"
					subjects={filteredSubjects}
					selectedTopics={selectedTopics}
					setSelectedTopics={setSelectedTopics}
					comboMaps={comboMaps}
					setComboMaps={setComboMaps}
					topicFormData={topicFormData}
					setTopicFormData={setTopicFormData}
					totalSelectedMCQs={totalSelectedMCQs}
				/>

				<div className="mt-8 flex justify-center">
					<BottomActionsBar
						mcqs={{ value: totalSelectedMCQs, cap: mcqCap }}
						count={{ value: totalSelectedTopics, label: "topics" }}
						onReset={resetTest}
						onAttempt={handleAttemptSubjectTest}
						loading={loadingParams || attempting}
						attemptDisabled={
							selectedTopics.length === 0 || loadingParams || attempting
						}
					/>
				</div>

				<TestSelectionModal
					open={showTestModal}
					tests={tests}
					selectedPrep={selectedPrep}
					loading={loadingTests}
					error={testsError}
					onClose={() => setShowTestModal(false)}
					onSelect={setSelectedPrep}
				/>
			</div>
		</div>
	);
}

export const Route = createFileRoute("/(app)/_mainlayout/t/subject")({
	component: SubjectTestPage,
});
