import { useState } from "react";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>ontent,
	<PERSON><PERSON><PERSON>eader,
	DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Search, X } from "react-feather";
import {
	fuzzySearch,
	searchableItems,
	SearchableItem,
} from "@/lib/search-data";
import { useNavigate } from "@tanstack/react-router";

interface MobileSearchDialogProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
}

const MobileSearchDialog = ({
	open,
	onOpenChange,
}: MobileSearchDialogProps) => {
	const [searchQuery, setSearchQuery] = useState("");
	const [searchResults, setSearchResults] = useState<SearchableItem[]>([]);
	const navigate = useNavigate();

	// Handle search input changes
	const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const query = e.target.value;
		setSearchQuery(query);

		if (query.trim()) {
			const results = fuzzySearch(query, searchableItems);
			setSearchResults(results);
		} else {
			setSearchResults([]);
		}
	};

	// Handle item click
	const handleItemClick = (item: SearchableItem) => {
		navigate({ to: item.href });
		onOpenChange(false);
		setSearchQuery("");
		setSearchResults([]);
	};

	// Handle keyboard events
	const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
		if (e.key === "Enter" && searchResults.length > 0 && searchResults[0]) {
			handleItemClick(searchResults[0]);
		}
	};

	// Handle dialog close
	const handleClose = () => {
		onOpenChange(false);
		setSearchQuery("");
		setSearchResults([]);
	};

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="sm:max-w-md p-0 gap-0">
				<DialogHeader className="px-4 py-3 border-b">
					<div className="flex items-center justify-between">
						<DialogTitle>Search App Features</DialogTitle>
						<button
							onClick={handleClose}
							className="p-1 hover:bg-gray-100 rounded"
						>
							<X size={16} />
						</button>
					</div>
				</DialogHeader>

				<div className="p-3">
					<div className="relative">
						<Input
							startIcon={Search}
							placeholder="Search features, pages..."
							value={searchQuery}
							onChange={handleSearchChange}
							onKeyDown={handleKeyDown}
							className="w-full"
							autoFocus
						/>
					</div>
				</div>

				<div className="max-h-80 overflow-y-auto">
					{searchQuery && searchResults.length === 0 && (
						<div className="p-3 text-center text-gray-500">
							<p className="text-sm">No results found</p>
							<p className="text-xs">
								Try searching for "dashboard", "test", "analytics", etc.
							</p>
						</div>
					)}

					{searchResults.length > 0 && (
						<div className="pb-2">
							<div className="text-xs text-gray-400 px-3 py-2 font-medium border-b">
								{searchResults.length} result
								{searchResults.length !== 1 ? "s" : ""} found
							</div>
							{searchResults.map((item) => (
								<button
									key={item.id}
									onClick={() => handleItemClick(item)}
									className="w-full flex items-center gap-3 p-3 hover:bg-gray-50 transition-colors text-left border-b border-gray-50 last:border-b-0"
								>
									<div className="flex-shrink-0">
										<div className="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center">
											<item.icon size={16} className="text-gray-600" />
										</div>
									</div>
									<div className="flex-1 min-w-0">
										<div className="flex items-center gap-2 mb-1">
											<h3 className="text-sm font-medium text-gray-900 truncate">
												{item.title}
											</h3>
											<span className="text-xs bg-gray-100 text-gray-600 px-2 py-0.5 rounded-full flex-shrink-0">
												{item.category}
											</span>
										</div>
										<p className="text-xs text-gray-500 truncate">
											{item.description}
										</p>
									</div>
								</button>
							))}
						</div>
					)}

					{!searchQuery && (
						<div className="p-3 text-center text-gray-500">
							<Search size={28} className="mx-auto mb-2 text-gray-300" />
							<p className="text-sm font-medium">Search App Features</p>
							<p className="text-xs">
								Find pages like Dashboard, Tests, Analytics, and more
							</p>
						</div>
					)}
				</div>
			</DialogContent>
		</Dialog>
	);
};

export default MobileSearchDialog;
