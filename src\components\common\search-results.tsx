import { useNavigate } from "@tanstack/react-router";
import { SearchableItem } from "@/lib/search-data";
import { ExternalLink } from "react-feather";

interface SearchResultsProps {
	results: SearchableItem[];
	onItemClick: () => void;
}

const SearchResults = ({ results, onItemClick }: SearchResultsProps) => {
	const navigate = useNavigate();

	const handleItemClick = (item: SearchableItem) => {
		onItemClick(); // Close search results
		navigate({ to: item.href });
	};

	if (results.length === 0) {
		return (
			<div className="absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-lg shadow-lg z-50 mt-1">
				<div className="p-4 text-center text-gray-500">
					<p>No results found</p>
					<p className="text-sm">
						Try searching for "dashboard", "test", "analytics", etc.
					</p>
				</div>
			</div>
		);
	}

	return (
		<div className="absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-lg shadow-lg z-50 mt-1 max-h-96 overflow-y-auto">
			<div className="p-1">
				<div className="text-xs text-gray-400 px-2 py-1.5 font-medium">
					{results.length} result{results.length !== 1 ? "s" : ""} found
				</div>
				{results.map((item) => (
					<button
						key={item.id}
						onClick={() => handleItemClick(item)}
						className="w-full flex items-center gap-2.5 p-2 hover:bg-gray-50 rounded-md transition-colors text-left"
					>
						<div className="flex-shrink-0">
							<div className="w-6 h-6 bg-gray-100 rounded-md flex items-center justify-center">
								<item.icon size={14} className="text-gray-600" />
							</div>
						</div>
						<div className="flex-1 min-w-0">
							<div className="flex items-center gap-2">
								<h3 className="text-sm font-medium text-gray-900 truncate">
									{item.title}
								</h3>
								<span className="text-xs bg-gray-100 text-gray-600 px-1.5 py-0.5 rounded-full flex-shrink-0">
									{item.category}
								</span>
							</div>
							<p className="text-xs text-gray-500 truncate">
								{item.description}
							</p>
						</div>
						<ExternalLink size={12} className="text-gray-400 flex-shrink-0" />
					</button>
				))}
			</div>
			<div className="border-t border-gray-100 p-2 bg-gray-50">
				<p className="text-xs text-gray-500 text-center">
					Press Enter to navigate to the first result
				</p>
			</div>
		</div>
	);
};

export default SearchResults;
